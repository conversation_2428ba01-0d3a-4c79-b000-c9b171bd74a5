# 算法在线推理服务部署文档

## 概述

本文档提供算法在线推理服务的部署指南，包括环境配置、依赖安装、服务启动等详细步骤。

## 系统要求

### 硬件要求
- **CPU**: 建议4核心以上
- **内存**: 建议8GB以上
- **存储**: 建议10GB以上可用空间
- **GPU**: 可选，支持CUDA的GPU可显著提升推理速度

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+) / macOS / Windows 10+
- **Python**: 3.8 - 3.11
- **CUDA**: 11.0+ (如使用GPU)

## 安装部署

### 1. 环境准备

#### 创建虚拟环境
```bash
# 使用conda
conda create -n yolo_api python=3.9
conda activate yolo_api

# 或使用venv
python -m venv yolo_api
source yolo_api/bin/activate  # Linux/macOS
# yolo_api\Scripts\activate  # Windows
```

#### 安装依赖
```bash
# 基础依赖
pip install flask ultralytics pillow opencv-python

# 如果使用GPU
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 生产环境依赖
pip install gunicorn
```

### 2. 模型准备

确保模型文件位于正确路径：
```
YOLO_outputs/
└── 20250701_121157/
    └── weights/
        └── best.pt
```

### 3. 配置文件

#### 服务配置
编辑 `yolo_api_server.py` 中的配置：
```python
# 服务端口
PORT = 5000

# 模型路径
MODEL_PATH = "YOLO_outputs/20250701_121157/weights/best.pt"

# 最大文件大小 (16MB)
MAX_CONTENT_LENGTH = 16 * 1024 * 1024
```

#### 日志配置
日志文件自动创建在 `logs/` 目录：
- 文件名: `yolo_api.log`
- 滚动策略: 10MB × 10个文件
- 编码: UTF-8

### 4. 启动服务

#### 开发环境
```bash
python yolo_api_server.py
```

#### 生产环境
```bash
# 使用Gunicorn (推荐)
gunicorn -w 4 -b 0.0.0.0:5000 yolo_api_server:app

# 后台运行
nohup gunicorn -w 4 -b 0.0.0.0:5000 yolo_api_server:app > gunicorn.log 2>&1 &
```

## Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "yolo_api_server:app"]
```

### 2. 创建requirements.txt
```txt
flask==2.3.3
ultralytics==8.0.196
pillow==10.0.0
opencv-python==********
gunicorn==21.2.0
```

### 3. 构建和运行
```bash
# 构建镜像
docker build -t yolo-api .

# 运行容器
docker run -d -p 5000:5000 --name yolo-api-server yolo-api

# 查看日志
docker logs yolo-api-server
```

## 服务管理

### 1. 系统服务 (Linux)

创建服务文件 `/etc/systemd/system/yolo-api.service`:
```ini
[Unit]
Description=YOLO API Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/yolo_api
Environment=PATH=/path/to/yolo_api/venv/bin
ExecStart=/path/to/yolo_api/venv/bin/gunicorn -w 4 -b 0.0.0.0:5000 yolo_api_server:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable yolo-api
sudo systemctl start yolo-api
sudo systemctl status yolo-api
```

### 2. 进程监控

#### 使用supervisor
安装supervisor：
```bash
sudo apt-get install supervisor
```

创建配置文件 `/etc/supervisor/conf.d/yolo-api.conf`:
```ini
[program:yolo-api]
command=/path/to/venv/bin/gunicorn -w 4 -b 0.0.0.0:5000 yolo_api_server:app
directory=/path/to/yolo_api
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/yolo-api.log
```

启动：
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start yolo-api
```

## 性能优化

### 1. 服务器配置
- **Worker进程数**: 建议设置为CPU核心数
- **内存限制**: 每个worker建议分配2-4GB内存
- **超时设置**: 根据模型推理时间调整

### 2. 负载均衡

#### 使用Nginx
```nginx
upstream yolo_api {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
    server 127.0.0.1:5003;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://yolo_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

## 监控和维护

### 1. 健康检查
```bash
# 检查服务状态
curl http://localhost:5000/health

# 监控脚本
#!/bin/bash
while true; do
    if ! curl -f http://localhost:5000/health > /dev/null 2>&1; then
        echo "Service is down, restarting..."
        systemctl restart yolo-api
    fi
    sleep 30
done
```

### 2. 日志管理
```bash
# 查看实时日志
tail -f logs/yolo_api.log

# 日志轮转已自动配置
# 最大文件大小: 10MB
# 保留文件数: 10个
```

### 3. 性能监控
- CPU使用率
- 内存使用率
- 请求响应时间
- 错误率统计

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径
   - 确认模型文件完整性
   - 检查文件权限

2. **内存不足**
   - 减少worker进程数
   - 优化图像预处理
   - 增加系统内存

3. **推理速度慢**
   - 启用GPU加速
   - 优化图像尺寸
   - 调整batch size

4. **端口占用**
   - 检查端口使用情况: `netstat -tlnp | grep 5000`
   - 修改配置文件中的端口号

### 日志分析
```bash
# 查看错误日志
grep "ERROR" logs/yolo_api.log

# 统计请求数量
grep "推理完成" logs/yolo_api.log | wc -l

# 分析响应时间
grep "total_time_ms" logs/yolo_api.log | tail -10
```

## 安全配置

### 1. 防火墙设置
```bash
# 只允许特定IP访问
sudo ufw allow from ***********/24 to any port 5000
```

### 2. 反向代理
建议使用Nginx作为反向代理，提供SSL终止和访问控制。

### 3. 访问限制
- 实现API密钥认证
- 设置请求频率限制
- 记录访问日志

---

**文档版本**: v1.0  
**最后更新**: 2025-07-14
