# 部署指南 - AEC Pack Dispensing Inspection System

本文档提供了重构后的算法在线推理服务的详细部署指南。

## 🏗️ 系统要求

### 硬件要求
- CPU: 4核心以上推荐
- 内存: 8GB以上推荐
- 存储: 50GB以上可用空间
- GPU: 可选，用于加速推理

### 软件要求
- 操作系统: Ubuntu 18.04+ / CentOS 7+ / Windows 10+
- Python: 3.8+
- 数据库: SQLite (内置)

## 🚀 快速部署

### 1. 环境准备

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip python3-venv -y

# 安装系统依赖
sudo apt install libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1 -y
```

### 2. 项目部署

```bash
# 创建项目目录
sudo mkdir -p /opt/aec-inference
cd /opt/aec-inference

# 克隆或复制项目文件
# git clone <repository-url> .

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 设置权限
sudo chown -R $USER:$USER /opt/aec-inference
```

### 3. 配置和启动

```bash
# 复制模型文件
cp /path/to/your/model.pt ./best.pt

# 使用默认配置启动
python main.py start

# 或使用自定义配置
cp config/default.json config/production.json
# 编辑配置文件...
python main.py start --config config/production.json
```

## 🐳 Docker部署

### 1. 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要目录
RUN mkdir -p logs image_storage/{original,annotated,thumbnails,temp}

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "main.py", "start"]
```

### 2. Docker Compose配置

```yaml
version: '3.8'

services:
  aec-inference:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - AEC_MODEL_PATH=/app/models/best.pt
      - AEC_API_HOST=0.0.0.0
      - AEC_API_PORT=5000
      - AEC_LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - aec-inference
    restart: unless-stopped
```

### 3. 运行Docker服务

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f aec-inference

# 停止服务
docker-compose down
```

## 🏭 生产环境部署

### 1. 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 创建Gunicorn配置
cat > gunicorn.conf.py << EOF
import multiprocessing

bind = "0.0.0.0:5000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
user = "www-data"
group = "www-data"
EOF

# 创建WSGI入口文件
cat > wsgi.py << EOF
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.api.app import create_app

app = create_app("config/production.json")

if __name__ == "__main__":
    app.run()
EOF

# 启动Gunicorn
gunicorn -c gunicorn.conf.py wsgi:app
```

### 2. 系统服务配置

```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/aec-inference.service << EOF
[Unit]
Description=AEC Pack Dispensing Inference Service
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/aec-inference
Environment=PATH=/opt/aec-inference/venv/bin
ExecStart=/opt/aec-inference/venv/bin/python main.py start --daemon
ExecStop=/opt/aec-inference/venv/bin/python main.py stop
Restart=always
RestartSec=10
PIDFile=/opt/aec-inference/aec-inference.pid

[Install]
WantedBy=multi-user.target
EOF

# 启用和启动服务
sudo systemctl daemon-reload
sudo systemctl enable aec-inference
sudo systemctl start aec-inference

# 查看状态
sudo systemctl status aec-inference
```

### 3. Nginx反向代理

```bash
# 创建Nginx配置
sudo tee /etc/nginx/sites-available/aec-inference << EOF
upstream aec_inference {
    server 127.0.0.1:5000;
}

server {
    listen 80;
    server_name your-domain.com;

    client_max_body_size 20M;
    client_body_timeout 300s;
    proxy_read_timeout 300s;

    location / {
        proxy_pass http://aec_inference;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 处理大文件上传
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # 静态文件缓存
    location /static/ {
        alias /opt/aec-inference/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        proxy_pass http://aec_inference/health;
        access_log off;
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/aec-inference /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## ⚙️ 配置管理

### 1. 生产环境配置

```json
{
  "database": {
    "path": "/opt/aec-inference/data/yolo_inference.db",
    "schema_file": "database_schema.sql",
    "backup_enabled": true,
    "backup_interval_hours": 6
  },
  "model": {
    "model_path": "/opt/aec-inference/models/best.pt",
    "confidence_threshold": 0.1,
    "warmup_enabled": true,
    "warmup_image_path": "/opt/aec-inference/resource/images/test_image.jpg"
  },
  "storage": {
    "base_path": "/opt/aec-inference/data/image_storage",
    "max_file_size_mb": 16,
    "cleanup_enabled": true,
    "cleanup_days": 30
  },
  "api": {
    "host": "127.0.0.1",
    "port": 5000,
    "debug": false,
    "max_content_length_mb": 16,
    "cors_enabled": false
  },
  "logging": {
    "level": "INFO",
    "log_dir": "/opt/aec-inference/logs",
    "max_file_size_mb": 10,
    "backup_count": 10
  },
  "statistics": {
    "enabled": true,
    "auto_refresh_seconds": 30,
    "max_timing_records": 1000
  },
  "false_positive": {
    "package_output_dir": "/opt/aec-inference/data/false_positive_packages",
    "auto_package_enabled": true,
    "package_interval_days": 7
  }
}
```

### 2. 环境变量配置

```bash
# 创建环境变量文件
cat > /opt/aec-inference/.env << EOF
# API配置
AEC_API_HOST=127.0.0.1
AEC_API_PORT=5000
AEC_API_DEBUG=false

# 模型配置
AEC_MODEL_PATH=/opt/aec-inference/models/best.pt
AEC_CONFIDENCE_THRESHOLD=0.1

# 数据库配置
AEC_DB_PATH=/opt/aec-inference/data/yolo_inference.db

# 存储配置
AEC_STORAGE_PATH=/opt/aec-inference/data/image_storage

# 日志配置
AEC_LOG_LEVEL=INFO
AEC_LOG_DIR=/opt/aec-inference/logs
EOF

# 加载环境变量
source /opt/aec-inference/.env
```
