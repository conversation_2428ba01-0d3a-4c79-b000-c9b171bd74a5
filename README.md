# 算法在线推理服务API调用文档

## 概述

算法在线推理服务是一个基于RESTful API的图像目标检测服务，提供高效的图像分析功能。服务支持多种图像格式，返回详细的检测结果和性能统计信息。

## 配置说明

在使用本文档中的示例代码前，请根据您的实际部署情况修改以下配置：

```bash
# 服务器地址配置
BASE_URL="http://***************:5000"  # 请根据实际情况修改hostname和端口
```

**常见配置示例：**
- 本地开发: `http://***************:5000`
- 局域网部署（暂无）: `http://*************:5000`
- 云服务器（暂无）: `http://your-domain.com:5000`

## 服务信息

- **默认地址**: `http://***************:5000`
- **支持的图像格式**: PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP
- **最大文件大小**: 16MB
- **响应格式**: JSON

## API端点

### 1. 服务状态检查

#### GET /health

检查服务健康状态和模型加载情况。

**请求示例:**
```bash
# 使用配置的BASE_URL
curl -X GET ${BASE_URL}/health

# 或直接使用具体地址
curl -X GET http://***************:5000/health
```

**响应示例:**
```json
{
  "status": "healthy",
  "model_loaded": true,
  "model_path": "YOLO_outputs/20250701_121157/weights/best.pt",
  "timestamp": "2025-07-14T10:30:00.123456",
  "supported_formats": ["png", "jpg", "jpeg", "gif", "bmp", "tiff", "webp"]
}
```

### 2. 图像推理

#### POST /predict

上传图像进行YOLO目标检测推理。

**请求参数:**

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| image | file | 是 | - | 要检测的图像文件 |
| confidence | float | 否 | 0.5 | 置信度阈值 (0.0-1.0) |
| return_image | bool | 否 | false | 是否返回标注后的图像 |

**请求示例:**

```bash
# 基本推理
curl -X POST -F "image=@test_image.jpg" ${BASE_URL}/predict

# 设置置信度阈值
curl -X POST -F "image=@test_image.jpg" -F "confidence=0.7" ${BASE_URL}/predict

# 返回标注图像
curl -X POST -F "image=@test_image.jpg" -F "return_image=true" ${BASE_URL}/predict
```

**HTTP请求格式:**
- **方法**: POST
- **URL**: `${BASE_URL}/predict`
- **Content-Type**: multipart/form-data
- **请求体**: 包含图像文件和可选参数

**实现要点:**
1. 使用HTTP POST方法发送multipart/form-data请求
2. 在请求体中包含名为'image'的文件字段
3. 可选参数作为表单字段发送：
   - `confidence`: 浮点数字符串 (如 "0.7")
   - `return_image`: 布尔值字符串 ("true" 或 "false")
4. 解析JSON格式的响应数据

**成功响应示例:**
```json
{
  "success": true,
  "timestamp": "2025-07-14T10:30:00.123456",
  "filename": "test_image.jpg",
  "image_size": {
    "width": 1920,
    "height": 1080
  },
  "model_path": "YOLO_outputs/20250701_121157/weights/best.pt",
  "confidence_threshold": 0.5,
  "detections_count": 3,
  "detections": [
    {
      "class_id": 0,
      "class_name": "person",
      "confidence": 0.8542,
      "bbox": {
        "x1": 100.25,
        "y1": 150.75,
        "x2": 300.50,
        "y2": 450.25,
        "center_x": 200.38,
        "center_y": 300.50,
        "width": 200.25,
        "height": 299.50
      }
    }
  ],
  "timing": {
    "preprocess_time_ms": 15.23,
    "inference_time_ms": 45.67,
    "postprocess_time_ms": 8.91,
    "image_annotation_time_ms": 12.34,
    "total_time_ms": 82.15,
    "breakdown": {
      "preprocess_percentage": 18.5,
      "inference_percentage": 55.6,
      "postprocess_percentage": 10.8,
      "annotation_percentage": 15.0
    }
  },
  "annotated_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

### 3. 上报误判

#### POST /api/false-positive/report

上报推理结果为误判，仅需提供 `session_id`。

**请求参数：**

| 参数名     | 类型   | 必需 | 说明           |
|------------|--------|------|----------------|
| session_id | string | 是   | 推理会话ID     |

**请求示例：**

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your_session_id_here"}' \
  ${BASE_URL}/api/false-positive/report
```

**HTTP请求格式：**
- 方法: POST
- URL: `${BASE_URL}/api/false-positive/report`
- Content-Type: application/json
- 请求体: JSON对象，包含`session_id`

**成功响应示例：**
```json
{
  "success": true,
  "message": "误判上报成功",
  "report_id": "b1e2c3d4-5678-90ab-cdef-1234567890ab",
  "session_id": "your_session_id_here",
  "timestamp": "2025-07-14T10:35:00.123456"
}
```

**错误响应示例：**

- 缺少session_id：
```json
{
  "error": "缺少必需字段: session_id",
  "message": "请提供session_id"
}
```
- session_id不存在：
```json
{
  "error": "推理记录不存在",
  "message": "未找到会话ID为 your_session_id_here 的推理记录"
}
```

**说明：**
- 仅需传递`session_id`，无需其他参数。
- `session_id`可通过推理接口(`/predict`)的响应获取。
- 上报后会生成唯一的`report_id`。

## 响应字段说明

### 检测结果字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| timestamp | string | 响应时间戳 (ISO格式) |
| filename | string | 上传的文件名 |
| image_size | object | 图像尺寸信息 |
| model_path | string | 使用的模型文件路径 |
| confidence_threshold | float | 使用的置信度阈值 |
| detections_count | integer | 检测到的对象数量 |
| detections | array | 检测结果数组 |
| timing | object | 性能统计信息 |
| annotated_image | string | Base64编码的标注图像 (可选) |

### 检测对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| class_id | integer | 类别ID |
| class_name | string | 类别名称 |
| confidence | float | 置信度分数 |
| bbox | object | 边界框信息 |

### 边界框字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| x1, y1 | float | 左上角坐标 |
| x2, y2 | float | 右下角坐标 |
| center_x, center_y | float | 中心点坐标 |
| width, height | float | 宽度和高度 |

### 性能统计字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| preprocess_time_ms | float | 图像预处理耗时 (毫秒) |
| inference_time_ms | float | 模型推理耗时 (毫秒) |
| postprocess_time_ms | float | 结果后处理耗时 (毫秒) |
| image_annotation_time_ms | float | 图像标注耗时 (毫秒) |
| total_time_ms | float | 总耗时 (毫秒) |
| breakdown | object | 各阶段时间占比 (百分比) |

## 错误处理

### 常见错误响应

#### 400 Bad Request - 请求参数错误
```json
{
  "error": "缺少图像文件",
  "message": "请在请求中包含名为'image'的文件"
}
```

#### 413 Payload Too Large - 文件过大
```json
{
  "error": "文件过大",
  "message": "上传的文件超过16MB限制"
}
```

#### 500 Internal Server Error - 服务器内部错误
```json
{
  "error": "推理失败",
  "message": "具体错误信息",
  "timestamp": "2025-07-14T10:30:00.123456"
}
```

#### 503 Service Unavailable - 模型未加载
```json
{
  "error": "模型未加载",
  "message": "YOLO模型未正确加载，请检查模型文件"
}
```

## 性能优化建议

1. **图像尺寸**: 建议上传图像尺寸不超过2048x2048像素，过大的图像会增加处理时间
2. **置信度阈值**: 适当提高置信度阈值可以减少误检，提高后处理速度
3. **并发调用**: 对于大量图像，建议使用多线程并发调用API以提高处理效率
4. **网络超时**: 推理可能需要较长时间，建议设置合适的HTTP请求超时时间





## 集成指南

### 通用实现步骤

1. **构建HTTP请求**
   - 使用POST方法
   - 设置Content-Type为multipart/form-data
   - 添加图像文件到请求体

2. **发送请求**
   - 目标URL: `${BASE_URL}/predict`
   - 包含必需的image字段
   - 根据需要添加可选参数

3. **处理响应**
   - 解析JSON响应
   - 检查success字段确认请求状态
   - 提取检测结果和时间统计信息

### 常见问题排查

- **文件格式错误**: 确保上传的是支持的图像格式
- **文件过大**: 检查文件大小是否超过16MB限制
- **网络超时**: 推理可能需要较长时间，建议设置合适的超时时间
- **参数错误**: 确保confidence值在0.0-1.0范围内

## 联系信息

如有问题或建议，请联系算法开发团队。

---

**文档版本**: v1.0
**最后更新**: 2025-07-14
