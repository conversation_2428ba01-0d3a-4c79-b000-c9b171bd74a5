# AEC Pack Dispensing Inspection System

基于YOLO的包装检测推理服务系统，提供图像上传、目标检测推理和结果可视化功能。

## 🚀 功能特性

- 🔍 **YOLO模型推理**: 支持自定义训练的YOLO模型进行目标检测
- 📊 **实时统计**: 提供API调用统计、推理时间统计和误报统计
- 🖼️ **图像管理**: 自动保存原始图像和标注图像，支持本地存储
- 📈 **数据可视化**: Web界面展示统计图表和历史数据
- 🔄 **误报处理**: 支持误报上报和数据打包功能
- 🛡️ **健壮性**: 完善的错误处理和日志记录
- ⚙️ **模块化设计**: 重构后的模块化架构，易于维护和扩展
- 🧪 **测试覆盖**: 完整的单元测试和集成测试

## 📁 项目结构

```
aec_pack_dispensing_inspection/
├── src/                          # 源代码目录
│   ├── api/                      # API相关模块
│   │   ├── app.py               # Flask应用主模块
│   │   └── routes.py            # API路由定义
│   ├── core/                     # 核心模块
│   │   ├── config.py            # 配置管理
│   │   ├── database_manager.py  # 数据库管理
│   │   └── image_storage.py     # 图像存储管理
│   ├── services/                 # 服务模块
│   │   ├── inference_service.py # YOLO推理服务
│   │   ├── statistics_service.py # 统计服务
│   │   ├── false_positive_service.py # 误报处理服务
│   │   └── service_manager.py   # 服务管理器
│   ├── utils/                    # 工具模块
│   │   └── logging_config.py    # 日志配置
│   └── web/                      # Web界面模块
├── tests/                        # 测试目录
│   ├── unit/                     # 单元测试
│   └── integration/              # 集成测试
├── config/                       # 配置文件目录
│   └── default.json             # 默认配置
├── scripts/                      # 脚本目录
│   ├── start_server.py          # 服务启动脚本
│   └── run_tests.py             # 测试运行脚本
├── docs/                         # 文档目录
├── main.py                       # 主入口文件
├── requirements.txt              # 依赖列表
└── README.md                     # 项目说明
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- OpenCV
- PyTorch (用于YOLO模型)
- Flask

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置系统

1. **使用默认配置**:
   ```bash
   # 直接使用默认配置启动
   python main.py start
   ```

2. **使用自定义配置文件**:
   ```bash
   # 复制默认配置并修改
   cp config/default.json config/my_config.json
   # 编辑配置文件...
   python main.py start --config config/my_config.json
   ```

3. **使用环境变量**:
   ```bash
   export AEC_API_PORT=8080
   export AEC_MODEL_PATH=/path/to/your/model.pt
   python main.py start
   ```

### 运行服务

```bash
# 启动服务 (前台运行)
python main.py start

# 启动服务 (守护进程模式)
python main.py start --daemon

# 停止服务
python main.py stop

# 重启服务
python main.py restart

# 查看服务状态
python main.py status

# 使用简化启动脚本
python scripts/start_server.py
```

### 访问服务

- Web界面: http://localhost:5000
- API健康检查: http://localhost:5000/health
- 误报浏览器: http://localhost:5000/false-positive-viewer
- 统计API: http://localhost:5000/api/statistics

## 📖 API接口

### 健康检查
```http
GET /health
```

### 图像推理
```http
POST /predict
Content-Type: multipart/form-data

参数:
- image: 图像文件 (必需)
- confidence: 置信度阈值 (可选，默认0.1)
- return_image: 是否返回标注图像 (可选，默认false)
```

### 统计信息
```http
GET /api/statistics
```

### 误报记录
```http
GET /api/false-positive/reports?status=pending&start_date=2024-01-01
```

### 创建误报数据包
```http
POST /api/false-positive/package
Content-Type: application/json

{
  "status": "pending",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31"
}
```

## ⚙️ 配置说明

### 配置文件格式 (JSON)

```json
{
  "database": {
    "path": "yolo_inference.db",
    "schema_file": "database_schema.sql",
    "backup_enabled": true,
    "backup_interval_hours": 24
  },
  "model": {
    "model_path": "best.pt",
    "confidence_threshold": 0.1,
    "warmup_enabled": true,
    "warmup_image_path": "resource/images/test_image.jpg"
  },
  "storage": {
    "base_path": "image_storage",
    "max_file_size_mb": 16,
    "cleanup_enabled": true,
    "cleanup_days": 30
  },
  "api": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false,
    "max_content_length_mb": 16,
    "cors_enabled": true
  },
  "logging": {
    "level": "INFO",
    "log_dir": "logs",
    "max_file_size_mb": 10,
    "backup_count": 10
  }
}
```

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `AEC_API_HOST` | API服务地址 | 0.0.0.0 |
| `AEC_API_PORT` | API服务端口 | 5000 |
| `AEC_MODEL_PATH` | YOLO模型路径 | best.pt |
| `AEC_CONFIDENCE_THRESHOLD` | 置信度阈值 | 0.1 |
| `AEC_DB_PATH` | 数据库路径 | yolo_inference.db |
| `AEC_STORAGE_PATH` | 图像存储路径 | image_storage |
| `AEC_LOG_LEVEL` | 日志级别 | INFO |

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python scripts/run_tests.py

# 只运行单元测试
python scripts/run_tests.py unit

# 只运行集成测试
python scripts/run_tests.py integration
```

### 测试覆盖

- **单元测试**: 测试各个模块的独立功能
- **集成测试**: 测试API端点和服务集成

## 🗄️ 数据库

系统使用SQLite数据库存储：
- 推理记录
- 检测结果
- API调用统计
- 误报上报记录

数据库文件: `yolo_inference.db`

## 📁 图像存储

图像按日期组织存储：
```
image_storage/
├── original/     # 原始图像
│   └── 2024/01/01/
├── annotated/    # 标注图像
│   └── 2024/01/01/
├── thumbnails/   # 缩略图
└── temp/         # 临时文件
```

## 🔄 误报处理

### 集成的误报处理功能

- **Web界面**: 通过 `/false-positive-viewer` 浏览误报记录
- **API接口**: 通过 `/api/false-positive/reports` 获取误报数据
- **数据打包**: 通过 `/api/false-positive/package` 创建数据包

### 数据包内容

```
false_positive_data_20240804_115517.zip
├── images/                     # 图像文件
│   └── report_<report_id>/     # 每个报告的图像
│       ├── original_<session_id>.jpg
│       └── annotated_<session_id>.jpg
├── metadata/                   # 元数据文件
│   └── report_<report_id>.json
├── package_summary.json        # 包摘要信息
└── README.md                   # 说明文档
```

## 🚀 部署

### 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd aec_pack_dispensing_inspection

# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py start
```

### 生产环境

```bash
# 使用守护进程模式
python main.py start --daemon

# 或使用systemd服务
sudo cp scripts/aec-inference.service /etc/systemd/system/
sudo systemctl enable aec-inference
sudo systemctl start aec-inference
```

### Docker部署

```bash
# 构建镜像
docker build -t aec-inference .

# 运行容器
docker run -d -p 5000:5000 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/data:/app/data \
  aec-inference
```

## 📊 监控和维护

### 日志管理

- 日志文件: `logs/yolo_api.log`
- 自动轮转: 10MB文件大小，保留10个备份
- 日志级别: 可通过配置调整

### 数据清理

```bash
# 清理旧的图像文件 (30天前)
python -c "
from src.core.image_storage import ImageStorage
storage = ImageStorage()
storage.cleanup_old_images(days=30)
"

# 清理旧的误报数据包
python -c "
from src.services.false_positive_service import FalsePositiveService
service = FalsePositiveService()
service.cleanup_old_packages(days=30)
"
```

### 性能监控

- 通过 `/api/statistics` 监控系统性能
- 推理时间统计
- API调用成功率
- 误报率统计

## 🔧 故障排除

### 常见问题

1. **模型加载失败**
   ```bash
   # 检查模型文件
   ls -la best.pt
   # 检查配置
   python main.py status
   ```

2. **数据库连接错误**
   ```bash
   # 检查数据库文件权限
   ls -la yolo_inference.db
   # 检查磁盘空间
   df -h
   ```

3. **图像保存失败**
   ```bash
   # 检查存储目录权限
   ls -la image_storage/
   # 创建必要目录
   mkdir -p image_storage/{original,annotated,thumbnails,temp}
   ```

4. **服务启动失败**
   ```bash
   # 查看详细错误信息
   python main.py start --config config/default.json
   # 检查端口占用
   netstat -tlnp | grep 5000
   ```

## 🔄 从旧版本迁移

如果你有旧版本的 `yolo_api_server.py`，可以按以下步骤迁移：

1. **备份数据**:
   ```bash
   cp yolo_inference.db yolo_inference.db.backup
   cp -r image_storage image_storage.backup
   ```

2. **更新代码**:
   ```bash
   # 使用新的启动方式
   python main.py start
   ```

3. **验证功能**:
   ```bash
   # 测试API
   curl http://localhost:5000/health
   # 运行测试
   python scripts/run_tests.py
   ```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

### 开发指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交Pull Request

### 代码规范

- 遵循PEP 8代码风格
- 添加适当的文档字符串
- 编写单元测试
- 更新相关文档
