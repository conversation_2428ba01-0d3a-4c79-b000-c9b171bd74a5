# 使用指南 - AEC Pack Dispensing Inspection System

## 🚀 快速开始

### 1. 启动服务

```bash
# 方式1: 使用run.sh脚本 (推荐)
./run.sh start

# 方式2: 使用main.py
python main.py start

# 方式3: 使用简化启动脚本
python scripts/start_server.py
```

### 2. 检查服务状态

```bash
# 查看详细状态
./run.sh status

# 或使用main.py
python main.py status
```

### 3. 测试所有API接口

```bash
# 方式1: 使用run.sh (快速测试)
./run.sh test-apis

# 方式2: 使用详细测试脚本
python scripts/test_all_apis.py

# 方式3: 使用快速测试脚本
./scripts/quick_test.sh

# 方式4: 启用测试模式进行测试（推荐）
export AEC_TEST_MODE=true
./run.sh test-apis
```

## 📋 run.sh 脚本使用

### 基本命令

```bash
# 启动服务
./run.sh start

# 停止服务
./run.sh stop

# 重启服务
./run.sh restart

# 查看状态
./run.sh status

# 运行单元测试和集成测试
./run.sh test

# 测试所有API接口
./run.sh test-apis

# 测试数据管理
./run.sh test-data stats     # 查看数据统计
./run.sh test-data cleanup   # 清理测试数据
./run.sh test-data backup    # 备份数据库

# 显示帮助
./run.sh help
```

### 状态检查示例

```bash
$ ./run.sh status
=== AEC Inference Service 状态 ===

服务状态:
  running: true
  pid: 12345
  config_file: None
  api_host: 0.0.0.0
  api_port: 5000
  pid_file: aec-inference.pid
  model_loaded: true
  database_connected: true
  storage_initialized: true

进程信息:
PID: 12345
日志文件: error.log
  PID  PPID CMD                          ELAPSED %CPU %MEM
12345     1 python main.py start --daemon   00:05  2.1  1.8

健康检查:
✅ 服务健康 (HTTP 200)
```

## 🧪 API测试

### 快速测试

```bash
$ ./run.sh test-apis
=== 测试所有API接口 ===
基础URL: http://localhost:5000

🔍 测试健康检查...
✅ 健康检查通过 (HTTP 200)

🏠 测试主页...
✅ 主页访问成功 (HTTP 200)

📊 测试统计API...
✅ 统计API访问成功 (HTTP 200)

🔄 测试误报记录API...
✅ 误报记录API访问成功 (HTTP 200)

🖼️  测试误报浏览器...
✅ 误报浏览器访问成功 (HTTP 200)

🤖 测试推理API (无文件)...
✅ 推理API正确拒绝无文件请求 (HTTP 400)

=== 测试结果 ===
总测试数: 6
成功: 6
失败: 0
🎉 所有API测试通过!
```

### 详细测试

```bash
$ python scripts/test_all_apis.py
🚀 开始API接口全面测试...
目标服务: http://localhost:5000
============================================================
✅ 服务可达 (HTTP 200)

🔍 测试健康检查端点...
✅ PASS 健康检查 (0.05s)
    状态: healthy, 模型已加载: True

🏠 测试主页...
✅ PASS 主页访问 (0.12s)
    HTML页面正常返回

📊 测试统计API...
✅ PASS 统计API (0.08s)
    返回数据完整，包含7个字段

🔄 测试误报记录API...
✅ PASS 误报记录API (0.03s)
    返回0条记录

🖼️  测试误报浏览器...
✅ PASS 误报浏览器 (0.02s)
    页面正常返回

🤖 测试推理API（无文件）...
✅ PASS 推理API无文件测试 (0.01s)
    正确拒绝: 缺少图像文件

🤖 测试推理API（有文件）...
✅ PASS 推理API文件测试 (1.23s)
    推理成功，检测到2个对象

📦 测试误报数据包API...
✅ PASS 误报数据包API (0.05s)
    正确处理无数据情况: 没有找到符合条件的误报记录

============================================================
📋 测试结果汇总
============================================================
总测试数: 8
通过: 8
失败: 0
成功率: 100.0%

============================================================
🎉 所有测试通过！API服务运行正常。
```

## 🔧 服务管理

### 启动选项

```bash
# 前台运行（开发模式）
python main.py start

# 后台运行（生产模式）
python main.py start --daemon

# 使用自定义配置
python main.py start --config config/production.json

# 使用run.sh（自动后台运行）
./run.sh start
```

### 日志查看

```bash
# 查看实时日志
tail -f logs/yolo_api.log

# 查看错误日志
tail -f error.log

# 查看最近的日志
./run.sh status  # 会显示最近的错误日志
```

### 故障排除

```bash
# 1. 检查服务状态
./run.sh status

# 2. 查看详细日志
tail -f logs/yolo_api.log

# 3. 测试API接口
./run.sh test-apis

# 4. 重启服务
./run.sh restart

# 5. 检查端口占用
netstat -tlnp | grep 5000
```

## 📊 API接口说明

### 主要端点

| 端点 | 方法 | 描述 | 测试命令 |
|------|------|------|----------|
| `/health` | GET | 健康检查 | `curl http://localhost:5000/health` |
| `/` | GET | 主页 | `curl http://localhost:5000/` |
| `/predict` | POST | 图像推理 | 需要上传图像文件 |
| `/api/statistics` | GET | 统计信息 | `curl http://localhost:5000/api/statistics` |
| `/api/false-positive/reports` | GET | 误报记录 | `curl http://localhost:5000/api/false-positive/reports` |
| `/api/false-positive/report` | POST | 提交误报上报 | 需要JSON数据 |
| `/api/false-positive/package` | POST | 创建误报数据包 | 需要JSON数据 |
| `/false-positive-viewer` | GET | 误报浏览器 | 浏览器访问 |

### 推理API使用示例

```bash
# 上传图像进行推理
curl -X POST \
  -F "image=@test.jpg" \
  -F "confidence=0.1" \
  -F "return_image=true" \
  http://localhost:5000/predict
```

### 误报上报API使用示例

```bash
# 提交误报上报
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "your-session-id",
    "false_positive_type": "false_detection",
    "reported_class_name": "detected_class",
    "correct_class_name": "actual_class",
    "report_reason": "误检测",
    "report_description": "详细描述误报情况",
    "reporter_info": "上报人信息"
  }' \
  http://localhost:5000/api/false-positive/report

# 获取误报记录
curl "http://localhost:5000/api/false-positive/reports?status=pending&start_date=2024-01-01"

# 创建误报数据包
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "status": "pending",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }' \
  http://localhost:5000/api/false-positive/package
```

## 🧪 测试框架

### 测试模式（重要！）

为了避免测试数据污染生产环境，系统提供了测试模式：

```bash
# 启用测试模式
export AEC_TEST_MODE=true

# 启动服务（测试模式）
./run.sh start

# 运行API测试（所有数据标记为测试数据）
./run.sh test-apis

# 查看数据统计
./run.sh test-data stats

# 清理测试数据
./run.sh test-data cleanup
```

### 运行单元测试

```bash
# 使用run.sh
./run.sh test

# 直接使用测试脚本
python scripts/run_tests.py unit
```

### 运行集成测试

```bash
# 运行所有测试
python scripts/run_tests.py

# 只运行集成测试
python scripts/run_tests.py integration
```

### 自定义测试

```bash
# 测试特定URL
python scripts/test_all_apis.py --url http://localhost:8080

# 启用测试模式进行测试
python scripts/test_all_apis.py --test-mode

# 输出JSON格式结果
python scripts/test_all_apis.py --json
```

## 🔄 从旧版本迁移

如果你之前使用的是单文件版本的 `yolo_api_server.py`：

### 1. 备份数据

```bash
cp yolo_inference.db yolo_inference.db.backup
cp -r image_storage image_storage.backup
```

### 2. 停止旧服务

```bash
# 如果使用旧的run.sh
./run.sh stop

# 或手动停止
pkill -f yolo_api_server.py
```

### 3. 启动新服务

```bash
# 使用新的启动方式
./run.sh start

# 或
python main.py start
```

### 4. 验证迁移

```bash
# 测试所有API
./run.sh test-apis

# 检查数据
curl http://localhost:5000/api/statistics
```

## 💡 最佳实践

### 开发环境

```bash
# 前台运行，便于调试
python main.py start

# 实时查看日志
tail -f logs/yolo_api.log
```

### 生产环境

```bash
# 后台运行
./run.sh start

# 定期检查状态
./run.sh status

# 定期测试API
./run.sh test-apis
```

### 监控和维护

```bash
# 设置定时任务检查服务状态
# 添加到crontab
*/5 * * * * /path/to/project/run.sh status > /dev/null || /path/to/project/run.sh restart
```

## ❓ 常见问题

### Q: 服务启动失败怎么办？

```bash
# 1. 检查日志
tail -f error.log

# 2. 检查端口占用
netstat -tlnp | grep 5000

# 3. 检查模型文件
ls -la best.pt

# 4. 检查配置
python -c "from src.core.config import get_config; print('配置加载成功')"
```

### Q: API测试失败怎么办？

```bash
# 1. 确认服务运行
./run.sh status

# 2. 检查网络连接
curl http://localhost:5000/health

# 3. 查看详细错误
python scripts/test_all_apis.py --json
```

### Q: 如何更改端口？

```bash
# 方式1: 环境变量
export AEC_API_PORT=8080
./run.sh start

# 方式2: 配置文件
# 编辑 config/default.json 中的 api.port
```

---

更多详细信息请参考 [README_NEW.md](README_NEW.md) 和 [DEPLOYMENT_NEW.md](DEPLOYMENT_NEW.md)。
