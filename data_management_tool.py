#!/usr/bin/env python3
"""
数据管理工具
用于YOLO推理服务的数据库维护、数据导出和图像库清理等管理功能

使用方法:
    python data_management_tool.py --help
    python data_management_tool.py stats
    python data_management_tool.py export --output data_export.json
    python data_management_tool.py cleanup --days 30
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# 导入数据库和图像存储模块
from database_manager import DatabaseManager
from image_storage import ImageStorage

class DataManagementTool:
    """数据管理工具类"""

    def __init__(self, db_path: str = "yolo_inference.db",
                 schema_file: str = "database_schema.sql",
                 storage_path: str = "image_storage"):
        """
        初始化数据管理工具

        Args:
            db_path: 数据库文件路径
            schema_file: 数据库架构文件路径
            storage_path: 图像存储路径
        """
        self.logger = self._setup_logging()

        try:
            self.db_manager = DatabaseManager(db_path, schema_file)
            self.image_storage = ImageStorage(storage_path)
            self.logger.info("数据管理工具初始化成功")
        except Exception as e:
            self.logger.error(f"数据管理工具初始化失败: {e}")
            raise

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)

        # 清除已有的处理器
        logger.handlers.clear()

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)

        logger.addHandler(console_handler)
        return logger

    def show_statistics(self):
        """显示统计信息"""
        try:
            print("=" * 60)
            print("YOLO推理服务 - 数据统计")
            print("=" * 60)

            # 数据库统计
            db_stats = self.db_manager.get_database_stats()
            print("\n📊 数据库统计:")
            print(f"  推理记录数: {db_stats.get('inference_records_count', 0)}")
            print(f"  检测结果数: {db_stats.get('detection_results_count', 0)}")
            print(f"  图像信息数: {db_stats.get('image_info_count', 0)}")
            print(f"  统计汇总数: {db_stats.get('statistics_summary_count', 0)}")
            print(f"  数据库大小: {db_stats.get('database_size_mb', 0)} MB")
            print(f"  最早记录: {db_stats.get('earliest_record', 'N/A')}")
            print(f"  最新记录: {db_stats.get('latest_record', 'N/A')}")

            # 图像存储统计
            storage_stats = self.image_storage.get_storage_statistics()
            print(f"\n📁 图像存储统计:")
            print(f"  总文件数: {storage_stats.get('total_files', 0)}")
            print(f"  总大小: {storage_stats.get('total_size_mb', 0)} MB")
            print(f"  存储路径: {storage_stats.get('storage_path', 'N/A')}")

            for img_type, type_stats in storage_stats.get('by_type', {}).items():
                print(f"    {img_type}: {type_stats['file_count']} 文件, {type_stats['size_mb']} MB")

            # 最近7天统计
            recent_stats = self.db_manager.get_daily_statistics(days=7)
            if recent_stats:
                print(f"\n📈 最近7天统计:")
                for stat in recent_stats[:7]:
                    print(f"  {stat['date']}: {stat['total_inferences']} 次推理, "
                          f"{stat['successful_inferences']} 成功, "
                          f"{stat['total_detections']} 检测")

            # 类别统计
            class_stats = self.db_manager.get_class_statistics()
            if class_stats:
                print(f"\n🏷️  检测类别统计 (前10):")
                for stat in class_stats[:10]:
                    print(f"  {stat['class_name']}: {stat['detection_count']} 次检测, "
                          f"平均置信度 {stat['avg_confidence']:.3f}")

            print("=" * 60)

        except Exception as e:
            self.logger.error(f"显示统计信息失败: {e}")
            raise

    def export_data(self, output_file: str,
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   include_images: bool = False):
        """
        导出数据

        Args:
            output_file: 输出文件路径
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            include_images: 是否包含图像数据
        """
        try:
            print(f"开始导出数据到: {output_file}")

            # 构建查询条件
            export_data = {
                "export_info": {
                    "timestamp": datetime.now().isoformat(),
                    "start_date": start_date,
                    "end_date": end_date,
                    "include_images": include_images
                },
                "statistics": {},
                "records": [],
                "daily_stats": [],
                "class_stats": []
            }

            # 导出统计信息
            export_data["statistics"] = self.db_manager.get_database_stats()

            # 导出推理记录
            records = self.db_manager.get_recent_inferences(limit=10000)  # 大量数据

            # 如果指定了日期范围，过滤记录
            if start_date or end_date:
                filtered_records = []
                for record in records:
                    record_date = record['created_at'][:10]  # 提取日期部分

                    if start_date and record_date < start_date:
                        continue
                    if end_date and record_date > end_date:
                        continue

                    filtered_records.append(record)

                records = filtered_records

            # 为每个记录添加检测结果
            for record in records:
                session_id = record['session_id']

                # 获取检测结果
                detections = self.db_manager.get_detection_results(session_id)
                record['detections'] = detections

                # 获取图像信息
                images = self.db_manager.get_image_info(session_id)
                record['images'] = images

                # 如果需要包含图像数据
                if include_images:
                    for image in images:
                        if self.image_storage.image_exists(image['file_path']):
                            # 这里可以添加base64编码的图像数据
                            # 但会显著增加文件大小，所以默认不包含
                            pass

            export_data["records"] = records

            # 导出每日统计
            export_data["daily_stats"] = self.db_manager.get_daily_statistics(days=90)

            # 导出类别统计
            export_data["class_stats"] = self.db_manager.get_class_statistics()

            # 写入文件
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 数据导出完成:")
            print(f"  导出记录数: {len(records)}")
            print(f"  文件大小: {output_path.stat().st_size / 1024 / 1024:.2f} MB")
            print(f"  输出文件: {output_path}")

        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            raise

    def cleanup_data(self, days_to_keep: int = 90, dry_run: bool = False):
        """
        清理旧数据

        Args:
            days_to_keep: 保留的天数
            dry_run: 是否为试运行（不实际删除）
        """
        try:
            print(f"开始清理 {days_to_keep} 天前的数据...")
            if dry_run:
                print("⚠️  试运行模式 - 不会实际删除数据")

            # 清理数据库记录
            if not dry_run:
                db_cleanup = self.db_manager.cleanup_old_records(days_to_keep)
                print(f"✅ 数据库清理完成:")
                print(f"  删除推理记录: {db_cleanup[0]}")
                print(f"  删除检测结果: {db_cleanup[1]}")
                print(f"  删除图像信息: {db_cleanup[2]}")
            else:
                # 试运行：计算将要删除的记录数
                cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')
                print(f"将删除 {cutoff_date} 之前的记录")

            # 清理图像文件
            if not dry_run:
                storage_cleanup = self.image_storage.cleanup_old_images(days_to_keep)
                print(f"✅ 图像存储清理完成:")
                print(f"  删除原始图像: {storage_cleanup['original_deleted']}")
                print(f"  删除标注图像: {storage_cleanup['annotated_deleted']}")
                print(f"  删除缩略图: {storage_cleanup['thumbnails_deleted']}")
                print(f"  释放空间: {storage_cleanup['total_size_freed_mb']:.2f} MB")
            else:
                storage_stats = self.image_storage.get_storage_statistics()
                print(f"当前图像存储: {storage_stats['total_files']} 文件, "
                      f"{storage_stats['total_size_mb']:.2f} MB")

        except Exception as e:
            self.logger.error(f"清理数据失败: {e}")
            raise

    def update_statistics(self, days: int = 7):
        """
        更新统计数据

        Args:
            days: 更新最近几天的统计
        """
        try:
            print(f"更新最近 {days} 天的统计数据...")

            for i in range(days):
                target_date = date.today() - timedelta(days=i)
                self.db_manager.update_daily_statistics(target_date)
                print(f"  更新 {target_date} 的统计数据")

            print("✅ 统计数据更新完成")

        except Exception as e:
            self.logger.error(f"更新统计数据失败: {e}")
            raise

    def verify_data_integrity(self):
        """验证数据完整性"""
        try:
            print("开始验证数据完整性...")

            issues = []

            # 检查推理记录和检测结果的一致性
            records = self.db_manager.get_recent_inferences(limit=1000)
            for record in records:
                session_id = record['session_id']
                detections = self.db_manager.get_detection_results(session_id)

                # 检查检测数量是否一致
                if len(detections) != record['detections_count']:
                    issues.append(f"会话 {session_id}: 检测数量不一致 "
                                f"(记录: {record['detections_count']}, 实际: {len(detections)})")

            # 检查图像文件是否存在
            image_records = self.db_manager.get_recent_inferences(limit=100)
            for record in image_records:
                session_id = record['session_id']
                images = self.db_manager.get_image_info(session_id)

                for image in images:
                    if not self.image_storage.image_exists(image['file_path']):
                        issues.append(f"图像文件不存在: {image['file_path']} (会话: {session_id})")

            if issues:
                print(f"❌ 发现 {len(issues)} 个数据完整性问题:")
                for issue in issues[:10]:  # 只显示前10个问题
                    print(f"  - {issue}")
                if len(issues) > 10:
                    print(f"  ... 还有 {len(issues) - 10} 个问题")
            else:
                print("✅ 数据完整性检查通过")

            return len(issues) == 0

        except Exception as e:
            self.logger.error(f"验证数据完整性失败: {e}")
            raise

    def backup_database(self, backup_path: str):
        """
        备份数据库

        Args:
            backup_path: 备份文件路径
        """
        try:
            import shutil

            db_path = Path(self.db_manager.db_path)
            backup_path = Path(backup_path)

            # 确保备份目录存在
            backup_path.parent.mkdir(parents=True, exist_ok=True)

            # 复制数据库文件
            shutil.copy2(db_path, backup_path)

            backup_size = backup_path.stat().st_size / 1024 / 1024
            print(f"✅ 数据库备份完成:")
            print(f"  源文件: {db_path}")
            print(f"  备份文件: {backup_path}")
            print(f"  文件大小: {backup_size:.2f} MB")

        except Exception as e:
            self.logger.error(f"备份数据库失败: {e}")
            raise

def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description='YOLO推理服务数据管理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python data_management_tool.py stats                    # 显示统计信息
  python data_management_tool.py export --output data.json # 导出数据
  python data_management_tool.py cleanup --days 30        # 清理30天前的数据
  python data_management_tool.py verify                   # 验证数据完整性
  python data_management_tool.py backup --output backup.db # 备份数据库
        """
    )

    parser.add_argument('--db-path', default='yolo_inference.db',
                       help='数据库文件路径 (默认: yolo_inference.db)')
    parser.add_argument('--schema-file', default='database_schema.sql',
                       help='数据库架构文件路径 (默认: database_schema.sql)')
    parser.add_argument('--storage-path', default='image_storage',
                       help='图像存储路径 (默认: image_storage)')

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # stats 命令
    subparsers.add_parser('stats', help='显示统计信息')

    # export 命令
    export_parser = subparsers.add_parser('export', help='导出数据')
    export_parser.add_argument('--output', required=True, help='输出文件路径')
    export_parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    export_parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    export_parser.add_argument('--include-images', action='store_true',
                              help='包含图像数据 (会显著增加文件大小)')

    # cleanup 命令
    cleanup_parser = subparsers.add_parser('cleanup', help='清理旧数据')
    cleanup_parser.add_argument('--days', type=int, default=90,
                               help='保留的天数 (默认: 90)')
    cleanup_parser.add_argument('--dry-run', action='store_true',
                               help='试运行，不实际删除数据')

    # update-stats 命令
    update_parser = subparsers.add_parser('update-stats', help='更新统计数据')
    update_parser.add_argument('--days', type=int, default=7,
                              help='更新最近几天的统计 (默认: 7)')

    # verify 命令
    subparsers.add_parser('verify', help='验证数据完整性')

    # backup 命令
    backup_parser = subparsers.add_parser('backup', help='备份数据库')
    backup_parser.add_argument('--output', required=True, help='备份文件路径')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    try:
        # 初始化数据管理工具
        tool = DataManagementTool(
            db_path=args.db_path,
            schema_file=args.schema_file,
            storage_path=args.storage_path
        )

        # 执行命令
        if args.command == 'stats':
            tool.show_statistics()

        elif args.command == 'export':
            tool.export_data(
                output_file=args.output,
                start_date=args.start_date,
                end_date=args.end_date,
                include_images=args.include_images
            )

        elif args.command == 'cleanup':
            tool.cleanup_data(
                days_to_keep=args.days,
                dry_run=args.dry_run
            )

        elif args.command == 'update-stats':
            tool.update_statistics(days=args.days)

        elif args.command == 'verify':
            success = tool.verify_data_integrity()
            sys.exit(0 if success else 1)

        elif args.command == 'backup':
            tool.backup_database(args.output)

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()