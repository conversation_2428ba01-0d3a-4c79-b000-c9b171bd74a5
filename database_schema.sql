-- YOLO推理服务数据库架构
-- 用于存储推理记录、检测结果和图像信息
-- 数据库类型: SQLite

-- 推理记录表 - 存储每次推理的基本信息
CREATE TABLE IF NOT EXISTS inference_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,                    -- 推理会话ID (UUID)
    filename TEXT NOT NULL,                      -- 原始文件名
    file_size INTEGER NOT NULL,                  -- 文件大小(字节)
    image_width INTEGER NOT NULL,                -- 图像宽度
    image_height INTEGER NOT NULL,               -- 图像高度
    model_path TEXT NOT NULL,                    -- 使用的模型路径
    confidence_threshold REAL NOT NULL,          -- 置信度阈值
    detections_count INTEGER NOT NULL DEFAULT 0, -- 检测到的对象数量

    -- 时间信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    inference_timestamp TEXT NOT NULL,           -- 推理时间戳(ISO格式)

    -- 性能统计 (毫秒)
    preprocess_time_ms REAL NOT NULL,
    inference_time_ms REAL NOT NULL,
    postprocess_time_ms REAL NOT NULL,
    image_annotation_time_ms REAL DEFAULT 0,
    total_time_ms REAL NOT NULL,

    -- 图像存储信息
    original_image_path TEXT,                    -- 原始图像存储路径
    annotated_image_path TEXT,                   -- 标注图像存储路径
    has_annotated_image BOOLEAN DEFAULT FALSE,   -- 是否生成了标注图像

    -- 请求信息
    client_ip TEXT,                              -- 客户端IP
    user_agent TEXT,                             -- 用户代理

    -- 状态信息
    status TEXT DEFAULT 'success',               -- 推理状态: success, error
    error_message TEXT,                          -- 错误信息(如果有)

    -- 索引字段
    UNIQUE(session_id)
);

-- 检测结果表 - 存储每个检测到的对象详细信息
CREATE TABLE IF NOT EXISTS detection_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,                    -- 关联推理记录
    detection_index INTEGER NOT NULL,            -- 在该次推理中的检测序号

    -- 分类信息
    class_id INTEGER NOT NULL,                   -- 类别ID
    class_name TEXT NOT NULL,                    -- 类别名称
    confidence REAL NOT NULL,                    -- 置信度

    -- 边界框信息
    bbox_x1 REAL NOT NULL,                       -- 左上角X坐标
    bbox_y1 REAL NOT NULL,                       -- 左上角Y坐标
    bbox_x2 REAL NOT NULL,                       -- 右下角X坐标
    bbox_y2 REAL NOT NULL,                       -- 右下角Y坐标
    bbox_center_x REAL NOT NULL,                 -- 中心点X坐标
    bbox_center_y REAL NOT NULL,                 -- 中心点Y坐标
    bbox_width REAL NOT NULL,                    -- 宽度
    bbox_height REAL NOT NULL,                   -- 高度

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (session_id) REFERENCES inference_records(session_id)
);

-- 图像信息表 - 存储图像文件的详细信息
CREATE TABLE IF NOT EXISTS image_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,                    -- 关联推理记录
    image_type TEXT NOT NULL,                    -- 图像类型: original, annotated

    -- 文件信息
    file_path TEXT NOT NULL,                     -- 文件存储路径
    file_name TEXT NOT NULL,                     -- 文件名
    file_size INTEGER NOT NULL,                  -- 文件大小(字节)
    file_format TEXT NOT NULL,                   -- 文件格式: jpg, png等

    -- 图像属性
    width INTEGER NOT NULL,                      -- 图像宽度
    height INTEGER NOT NULL,                     -- 图像高度
    channels INTEGER DEFAULT 3,                  -- 颜色通道数

    -- 哈希值用于去重和完整性检查
    md5_hash TEXT,                               -- MD5哈希值
    sha256_hash TEXT,                            -- SHA256哈希值

    -- 时间信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (session_id) REFERENCES inference_records(session_id),
    UNIQUE(session_id, image_type)
);

-- 统计汇总表 - 用于快速查询统计信息
CREATE TABLE IF NOT EXISTS statistics_summary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date_key TEXT NOT NULL,                      -- 日期键 (YYYY-MM-DD)

    -- 推理统计
    total_inferences INTEGER DEFAULT 0,          -- 总推理次数
    successful_inferences INTEGER DEFAULT 0,     -- 成功推理次数
    failed_inferences INTEGER DEFAULT 0,         -- 失败推理次数

    -- 检测统计
    total_detections INTEGER DEFAULT 0,          -- 总检测数量
    unique_classes INTEGER DEFAULT 0,            -- 检测到的唯一类别数

    -- 性能统计
    avg_inference_time_ms REAL DEFAULT 0,        -- 平均推理时间
    min_inference_time_ms REAL DEFAULT 0,        -- 最小推理时间
    max_inference_time_ms REAL DEFAULT 0,        -- 最大推理时间

    -- 存储统计
    total_images_stored INTEGER DEFAULT 0,       -- 存储的图像总数
    total_storage_size_mb REAL DEFAULT 0,        -- 总存储大小(MB)

    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(date_key)
);

-- 误判上报表 - 存储用户上报的误判信息
CREATE TABLE IF NOT EXISTS false_positive_reports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,                    -- 关联推理记录
    report_id TEXT NOT NULL,                     -- 上报ID (UUID)
    
    -- 上报信息
    reporter_info TEXT,                          -- 上报人信息
    report_reason TEXT,                          -- 上报原因
    report_description TEXT,                     -- 详细描述
    
    -- 误判类型
    false_positive_type TEXT NOT NULL,          -- 误判类型: false_detection, wrong_class, low_confidence
    reported_class_name TEXT,                    -- 被误判的类别名称
    correct_class_name TEXT,                     -- 正确的类别名称(如果适用)
    
    -- 状态信息
    status TEXT DEFAULT 'pending',              -- 状态: pending, confirmed, rejected, processed
    processed_by TEXT,                           -- 处理人
    processed_at DATETIME,                       -- 处理时间
    processing_notes TEXT,                       -- 处理备注
    
    -- 时间信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 客户端信息
    client_ip TEXT,                              -- 客户端IP
    user_agent TEXT,                             -- 用户代理
    
    FOREIGN KEY (session_id) REFERENCES inference_records(session_id),
    UNIQUE(report_id)
);

-- API调用统计表 - 存储API端点的调用统计
CREATE TABLE IF NOT EXISTS api_call_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    endpoint TEXT NOT NULL,                    -- API端点名称
    method TEXT NOT NULL,                      -- HTTP方法 (GET, POST, etc.)
    total_calls INTEGER DEFAULT 0,             -- 总调用次数
    successful_calls INTEGER DEFAULT 0,        -- 成功调用次数
    failed_calls INTEGER DEFAULT 0,            -- 失败调用次数
    last_called_at DATETIME,                   -- 最后调用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(endpoint, method)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_inference_records_created_at ON inference_records(created_at);
CREATE INDEX IF NOT EXISTS idx_inference_records_status ON inference_records(status);
CREATE INDEX IF NOT EXISTS idx_inference_records_model_path ON inference_records(model_path);
CREATE INDEX IF NOT EXISTS idx_detection_results_session_id ON detection_results(session_id);
CREATE INDEX IF NOT EXISTS idx_detection_results_class_name ON detection_results(class_name);
CREATE INDEX IF NOT EXISTS idx_detection_results_confidence ON detection_results(confidence);
CREATE INDEX IF NOT EXISTS idx_image_info_session_id ON image_info(session_id);
CREATE INDEX IF NOT EXISTS idx_image_info_image_type ON image_info(image_type);
CREATE INDEX IF NOT EXISTS idx_statistics_summary_date_key ON statistics_summary(date_key);
CREATE INDEX IF NOT EXISTS idx_false_positive_reports_session_id ON false_positive_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_false_positive_reports_status ON false_positive_reports(status);
CREATE INDEX IF NOT EXISTS idx_false_positive_reports_created_at ON false_positive_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_api_call_statistics_endpoint ON api_call_statistics(endpoint);

-- 创建视图以简化常用查询
-- 推理记录详细视图 - 包含检测结果统计
CREATE VIEW IF NOT EXISTS v_inference_details AS
SELECT
    ir.*,
    COUNT(dr.id) as actual_detections_count,
    GROUP_CONCAT(DISTINCT dr.class_name) as detected_classes,
    AVG(dr.confidence) as avg_confidence,
    MAX(dr.confidence) as max_confidence,
    MIN(dr.confidence) as min_confidence
FROM inference_records ir
LEFT JOIN detection_results dr ON ir.session_id = dr.session_id
GROUP BY ir.session_id;

-- 每日统计视图
CREATE VIEW IF NOT EXISTS v_daily_stats AS
SELECT
    DATE(created_at) as date,
    COUNT(*) as total_inferences,
    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_inferences,
    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed_inferences,
    SUM(detections_count) as total_detections,
    AVG(total_time_ms) as avg_total_time_ms,
    AVG(inference_time_ms) as avg_inference_time_ms,
    MIN(total_time_ms) as min_total_time_ms,
    MAX(total_time_ms) as max_total_time_ms
FROM inference_records
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 类别统计视图
CREATE VIEW IF NOT EXISTS v_class_stats AS
SELECT
    dr.class_name,
    COUNT(*) as detection_count,
    AVG(dr.confidence) as avg_confidence,
    MIN(dr.confidence) as min_confidence,
    MAX(dr.confidence) as max_confidence,
    COUNT(DISTINCT dr.session_id) as inference_sessions
FROM detection_results dr
GROUP BY dr.class_name
ORDER BY detection_count DESC;