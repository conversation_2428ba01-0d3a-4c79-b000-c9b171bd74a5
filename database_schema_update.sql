-- 数据库架构更新脚本
-- 添加测试标识字段以区分测试数据和生产数据

-- 为推理记录表添加测试标识
ALTER TABLE inference_records ADD COLUMN is_test BOOLEAN DEFAULT FALSE;

-- 为检测结果表添加测试标识
ALTER TABLE detection_results ADD COLUMN is_test BOOLEAN DEFAULT FALSE;

-- 为图像信息表添加测试标识
ALTER TABLE image_info ADD COLUMN is_test BOOLEAN DEFAULT FALSE;

-- 为误报上报表添加测试标识
ALTER TABLE false_positive_reports ADD COLUMN is_test BOOLEAN DEFAULT FALSE;

-- 为API调用统计表添加测试标识
ALTER TABLE api_call_statistics ADD COLUMN is_test BOOLEAN DEFAULT FALSE;

-- 为统计汇总表添加测试标识
ALTER TABLE statistics_summary ADD COLUMN is_test BOOLEAN DEFAULT FALSE;

-- 创建测试数据索引
CREATE INDEX IF NOT EXISTS idx_inference_records_is_test ON inference_records(is_test);
CREATE INDEX IF NOT EXISTS idx_detection_results_is_test ON detection_results(is_test);
CREATE INDEX IF NOT EXISTS idx_image_info_is_test ON image_info(is_test);
CREATE INDEX IF NOT EXISTS idx_false_positive_reports_is_test ON false_positive_reports(is_test);
CREATE INDEX IF NOT EXISTS idx_api_call_statistics_is_test ON api_call_statistics(is_test);
CREATE INDEX IF NOT EXISTS idx_statistics_summary_is_test ON statistics_summary(is_test);

-- 创建测试数据清理的存储过程（通过SQL实现）
-- 注意：SQLite不支持存储过程，这里提供清理测试数据的SQL语句

-- 清理测试数据的SQL语句（可以在需要时执行）
/*
-- 清理测试推理记录
DELETE FROM inference_records WHERE is_test = TRUE;

-- 清理测试检测结果
DELETE FROM detection_results WHERE is_test = TRUE;

-- 清理测试图像信息
DELETE FROM image_info WHERE is_test = TRUE;

-- 清理测试误报上报
DELETE FROM false_positive_reports WHERE is_test = TRUE;

-- 清理测试API统计
DELETE FROM api_call_statistics WHERE is_test = TRUE;

-- 清理测试统计汇总
DELETE FROM statistics_summary WHERE is_test = TRUE;

-- 重置自增ID（可选）
-- DELETE FROM sqlite_sequence WHERE name IN ('inference_records', 'detection_results', 'image_info', 'false_positive_reports', 'api_call_statistics', 'statistics_summary');
*/
