#!/usr/bin/env python3
"""
图像诊断脚本
用于分析误报数据包中图像缺失的原因
"""

import os
import sqlite3
import json
from datetime import datetime
from pathlib import Path
import logging
from database_manager import DatabaseManager
from image_storage import ImageStorage

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ImageDiagnostic:
    """图像诊断器"""
    
    def __init__(self, db_path="yolo_inference.db", image_storage_dir="image_storage"):
        self.db_path = db_path
        self.image_storage_dir = image_storage_dir
        self.db_manager = None
        self.image_storage = None
        
    def initialize(self):
        """初始化数据库和图像存储管理器"""
        try:
            self.db_manager = DatabaseManager(self.db_path)
            self.image_storage = ImageStorage(self.image_storage_dir)
            logger.info("诊断器初始化成功")
            return True
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def analyze_false_positive_images(self, status=None, start_date=None, end_date=None):
        """分析误报图像状态"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if status:
                    where_conditions.append("fpr.status = ?")
                    params.append(status)
                    
                if start_date:
                    where_conditions.append("DATE(fpr.created_at) >= ?")
                    params.append(start_date)
                    
                if end_date:
                    where_conditions.append("DATE(fpr.created_at) <= ?")
                    params.append(end_date)
                
                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                
                # 查询误报记录
                query = f"""
                    SELECT 
                        fpr.report_id,
                        fpr.session_id,
                        fpr.created_at,
                        ir.original_image_path,
                        ir.annotated_image_path,
                        ir.has_annotated_image,
                        ir.filename
                    FROM false_positive_reports fpr
                    LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
                    {where_clause}
                    ORDER BY fpr.created_at DESC
                """
                
                cursor.execute(query, params)
                reports = cursor.fetchall()
                
                # 分析结果
                analysis = {
                    "total_reports": len(reports),
                    "original_image_stats": {
                        "total": 0,
                        "exists": 0,
                        "missing": 0,
                        "missing_paths": []
                    },
                    "annotated_image_stats": {
                        "total": 0,
                        "exists": 0,
                        "missing": 0,
                        "missing_paths": []
                    },
                    "detailed_analysis": []
                }
                
                for report in reports:
                    report_id, session_id, created_at, original_path, annotated_path, has_annotated, filename = report
                    
                    # 分析原始图像
                    original_exists = False
                    if original_path:
                        analysis["original_image_stats"]["total"] += 1
                        original_exists = self.image_storage.image_exists(original_path)
                        if original_exists:
                            analysis["original_image_stats"]["exists"] += 1
                        else:
                            analysis["original_image_stats"]["missing"] += 1
                            analysis["original_image_stats"]["missing_paths"].append({
                                "report_id": report_id,
                                "session_id": session_id,
                                "path": original_path,
                                "created_at": created_at
                            })
                    
                    # 分析标注图像
                    annotated_exists = False
                    if annotated_path and has_annotated:
                        analysis["annotated_image_stats"]["total"] += 1
                        annotated_exists = self.image_storage.image_exists(annotated_path)
                        if annotated_exists:
                            analysis["annotated_image_stats"]["exists"] += 1
                        else:
                            analysis["annotated_image_stats"]["missing"] += 1
                            analysis["annotated_image_stats"]["missing_paths"].append({
                                "report_id": report_id,
                                "session_id": session_id,
                                "path": annotated_path,
                                "created_at": created_at
                            })
                    
                    # 详细分析
                    analysis["detailed_analysis"].append({
                        "report_id": report_id,
                        "session_id": session_id,
                        "created_at": created_at,
                        "filename": filename,
                        "original_image": {
                            "path": original_path,
                            "exists": original_exists
                        },
                        "annotated_image": {
                            "path": annotated_path,
                            "has_annotated": has_annotated,
                            "exists": annotated_exists
                        }
                    })
                
                return analysis
                
        except Exception as e:
            logger.error(f"分析失败: {e}")
            return None
    
    def check_storage_integrity(self):
        """检查存储完整性"""
        try:
            storage_stats = self.image_storage.get_storage_statistics()
            
            # 检查数据库中的图像路径
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查推理记录中的图像路径
                cursor.execute("""
                    SELECT session_id, original_image_path, annotated_image_path, has_annotated_image
                    FROM inference_records
                    WHERE original_image_path IS NOT NULL OR annotated_image_path IS NOT NULL
                """)
                
                inference_records = cursor.fetchall()
                
                integrity_check = {
                    "storage_statistics": storage_stats,
                    "inference_records_analysis": {
                        "total_records": len(inference_records),
                        "with_original": 0,
                        "with_annotated": 0,
                        "original_exists": 0,
                        "annotated_exists": 0,
                        "missing_original": 0,
                        "missing_annotated": 0
                    }
                }
                
                for record in inference_records:
                    session_id, original_path, annotated_path, has_annotated = record
                    
                    if original_path:
                        integrity_check["inference_records_analysis"]["with_original"] += 1
                        if self.image_storage.image_exists(original_path):
                            integrity_check["inference_records_analysis"]["original_exists"] += 1
                        else:
                            integrity_check["inference_records_analysis"]["missing_original"] += 1
                    
                    if annotated_path and has_annotated:
                        integrity_check["inference_records_analysis"]["with_annotated"] += 1
                        if self.image_storage.image_exists(annotated_path):
                            integrity_check["inference_records_analysis"]["annotated_exists"] += 1
                        else:
                            integrity_check["inference_records_analysis"]["missing_annotated"] += 1
                
                return integrity_check
                
        except Exception as e:
            logger.error(f"存储完整性检查失败: {e}")
            return None
    
    def generate_report(self, output_file="image_diagnostic_report.json"):
        """生成诊断报告"""
        try:
            # 分析误报图像
            fp_analysis = self.analyze_false_positive_images()
            
            # 检查存储完整性
            integrity_check = self.check_storage_integrity()
            
            # 生成综合报告
            report = {
                "generated_at": datetime.now().isoformat(),
                "false_positive_analysis": fp_analysis,
                "storage_integrity": integrity_check,
                "recommendations": []
            }
            
            # 生成建议
            if fp_analysis:
                missing_original = fp_analysis["original_image_stats"]["missing"]
                missing_annotated = fp_analysis["annotated_image_stats"]["missing"]
                
                if missing_original > 0:
                    report["recommendations"].append({
                        "type": "warning",
                        "message": f"发现 {missing_original} 个原始图像文件缺失",
                        "action": "检查图像存储配置和清理策略"
                    })
                
                if missing_annotated > 0:
                    report["recommendations"].append({
                        "type": "warning", 
                        "message": f"发现 {missing_annotated} 个标注图像文件缺失",
                        "action": "检查标注图像生成和存储逻辑"
                    })
            
            # 保存报告
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"诊断报告已生成: {output_file}")
            return report
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return None

def main():
    """主函数"""
    diagnostic = ImageDiagnostic()
    
    if not diagnostic.initialize():
        logger.error("初始化失败")
        return
    
    # 生成诊断报告
    report = diagnostic.generate_report()
    
    if report:
        print("\n=== 图像诊断报告 ===")
        print(f"生成时间: {report['generated_at']}")
        
        if report['false_positive_analysis']:
            fp_analysis = report['false_positive_analysis']
            print(f"\n误报记录总数: {fp_analysis['total_reports']}")
            
            print(f"\n原始图像统计:")
            print(f"  总数: {fp_analysis['original_image_stats']['total']}")
            print(f"  存在: {fp_analysis['original_image_stats']['exists']}")
            print(f"  缺失: {fp_analysis['original_image_stats']['missing']}")
            
            print(f"\n标注图像统计:")
            print(f"  总数: {fp_analysis['annotated_image_stats']['total']}")
            print(f"  存在: {fp_analysis['annotated_image_stats']['exists']}")
            print(f"  缺失: {fp_analysis['annotated_image_stats']['missing']}")
        
        if report['recommendations']:
            print(f"\n建议:")
            for rec in report['recommendations']:
                print(f"  [{rec['type'].upper()}] {rec['message']}")
                print(f"    建议操作: {rec['action']}")

if __name__ == "__main__":
    main() 