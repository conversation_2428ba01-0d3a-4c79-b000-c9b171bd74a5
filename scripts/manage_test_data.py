#!/usr/bin/env python3
"""
测试数据管理脚本
用于管理测试数据和生产数据的分离
"""

import sys
import os
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.core.database_manager import DatabaseManager
from src.core.config import get_config_manager


def show_statistics(db_manager: DatabaseManager):
    """显示测试数据统计"""
    print("📊 数据库统计信息")
    print("=" * 60)
    
    try:
        stats = db_manager.get_test_data_statistics()
        
        total_test = 0
        total_prod = 0
        total_all = 0
        
        for table, data in stats.items():
            test_count = data['test_data']
            prod_count = data['production_data']
            total_count = data['total']
            
            if test_count >= 0:  # 只显示有效数据
                print(f"{table:25} | 测试: {test_count:6} | 生产: {prod_count:6} | 总计: {total_count:6}")
                total_test += test_count
                total_prod += prod_count
                total_all += total_count
            else:
                print(f"{table:25} | 错误: 无法获取统计信息")
        
        print("-" * 60)
        print(f"{'总计':25} | 测试: {total_test:6} | 生产: {total_prod:6} | 总计: {total_all:6}")
        
        if total_all > 0:
            test_percentage = (total_test / total_all) * 100
            print(f"\n测试数据占比: {test_percentage:.1f}%")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")


def cleanup_test_data(db_manager: DatabaseManager, confirm: bool = False):
    """清理测试数据"""
    if not confirm:
        print("⚠️  此操作将删除所有标记为测试的数据，且不可恢复！")
        response = input("确认要继续吗？(输入 'yes' 确认): ")
        if response.lower() != 'yes':
            print("操作已取消")
            return
    
    print("🧹 开始清理测试数据...")
    
    try:
        cleanup_stats = db_manager.cleanup_test_data()
        
        print("\n清理结果:")
        print("-" * 40)
        
        total_cleaned = 0
        for table, count in cleanup_stats.items():
            if count > 0:
                print(f"✅ {table}: 清理了 {count} 条记录")
                total_cleaned += count
            elif count == 0:
                print(f"ℹ️  {table}: 无测试数据")
            else:
                print(f"❌ {table}: 清理失败")
        
        print("-" * 40)
        print(f"总计清理了 {total_cleaned} 条测试数据")
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")


def backup_database(db_path: str, backup_dir: str = "backups"):
    """备份数据库"""
    try:
        backup_path = Path(backup_dir)
        backup_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_path / f"database_backup_{timestamp}.db"
        
        import shutil
        shutil.copy2(db_path, backup_file)
        
        print(f"✅ 数据库已备份到: {backup_file}")
        return str(backup_file)
        
    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试数据管理工具")
    parser.add_argument("action", choices=["stats", "cleanup", "backup"], 
                       help="要执行的操作")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--db-path", help="数据库文件路径")
    parser.add_argument("--yes", "-y", action="store_true", 
                       help="自动确认操作（用于脚本自动化）")
    parser.add_argument("--backup-dir", default="backups", 
                       help="备份目录路径")
    
    args = parser.parse_args()
    
    # 获取配置
    try:
        config_manager = get_config_manager(args.config)
        config = config_manager.get_config()
        
        # 使用命令行参数或配置文件中的数据库路径
        db_path = args.db_path or config.database.path
        
        print(f"📁 使用数据库: {db_path}")
        
        # 创建数据库管理器（非测试模式，以便能看到所有数据）
        db_manager = DatabaseManager(
            db_path=db_path,
            schema_file=config.database.schema_file,
            test_mode=False
        )
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        sys.exit(1)
    
    # 执行操作
    if args.action == "stats":
        show_statistics(db_manager)
        
    elif args.action == "cleanup":
        # 先显示统计信息
        print("清理前的数据统计:")
        show_statistics(db_manager)
        print()
        
        # 执行清理
        cleanup_test_data(db_manager, args.yes)
        
        # 显示清理后的统计
        print("\n清理后的数据统计:")
        show_statistics(db_manager)
        
    elif args.action == "backup":
        backup_file = backup_database(db_path, args.backup_dir)
        if backup_file:
            print(f"备份文件大小: {Path(backup_file).stat().st_size / 1024 / 1024:.2f} MB")


if __name__ == "__main__":
    main()
