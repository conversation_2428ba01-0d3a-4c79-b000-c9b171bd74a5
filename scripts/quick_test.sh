#!/bin/bash

# 快速API测试脚本
# 用于快速验证所有API端点是否正常工作
# 测试数据将自动标记为测试数据

BASE_URL="http://localhost:5000"
FAILED_TESTS=0
TOTAL_TESTS=0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local name="$1"
    local url="$2"
    local method="${3:-GET}"
    local expected_code="${4:-200}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -n "🔍 测试 $name ... "
    
    if command -v curl > /dev/null; then
        if [ "$method" = "POST" ]; then
            response_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
                -H "X-Test-Request: true" \
                -H "User-Agent: AEC-Quick-Test/1.0" \
                "$url" 2>/dev/null)
        else
            response_code=$(curl -s -o /dev/null -w "%{http_code}" \
                -H "X-Test-Request: true" \
                -H "User-Agent: AEC-Quick-Test/1.0" \
                "$url" 2>/dev/null)
        fi
        
        if [ "$response_code" = "$expected_code" ]; then
            echo -e "${GREEN}✅ 通过${NC} (HTTP $response_code)"
        else
            echo -e "${RED}❌ 失败${NC} (HTTP $response_code, 期望 $expected_code)"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${YELLOW}⚠️  跳过${NC} (curl未安装)"
    fi
}

# 检查服务是否运行
check_service() {
    echo "🚀 检查服务状态..."
    
    if command -v curl > /dev/null; then
        if curl -s --connect-timeout 5 "$BASE_URL/health" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 服务正在运行${NC}"
            return 0
        else
            echo -e "${RED}❌ 服务未运行或不可达${NC}"
            echo "请先启动服务: ./run.sh start"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  无法检查服务状态 (curl未安装)${NC}"
        return 0
    fi
}

# 主测试流程
main() {
    echo "🧪 AEC Inference Service - 快速API测试"
    echo "目标服务: $BASE_URL"
    echo "=" * 50
    
    # 检查服务状态
    if ! check_service; then
        exit 1
    fi
    
    echo ""
    echo "开始API测试..."
    
    # 测试各个端点
    test_endpoint "健康检查" "$BASE_URL/health" "GET" "200"
    test_endpoint "主页" "$BASE_URL/" "GET" "200"
    test_endpoint "统计API" "$BASE_URL/api/statistics" "GET" "200"
    test_endpoint "误报记录API" "$BASE_URL/api/false-positive/reports" "GET" "200"
    test_endpoint "误报浏览器" "$BASE_URL/false-positive-viewer" "GET" "200"
    test_endpoint "推理API(无文件)" "$BASE_URL/predict" "POST" "400"
    test_endpoint "误报上报API(无数据)" "$BASE_URL/api/false-positive/report" "POST" "400"
    
    echo ""
    echo "=" * 50
    echo "📊 测试结果:"
    echo "总测试数: $TOTAL_TESTS"
    echo "成功: $((TOTAL_TESTS - FAILED_TESTS))"
    echo "失败: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！${NC}"
        exit 0
    else
        echo -e "${RED}⚠️  有 $FAILED_TESTS 个测试失败${NC}"
        echo ""
        echo "💡 建议:"
        echo "  1. 检查服务日志: tail -f logs/yolo_api.log"
        echo "  2. 查看服务状态: ./run.sh status"
        echo "  3. 运行详细测试: python scripts/test_all_apis.py"
        exit 1
    fi
}

# 运行主函数
main "$@"
