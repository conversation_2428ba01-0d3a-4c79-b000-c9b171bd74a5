#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import unittest
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_unit_tests():
    """运行单元测试"""
    print("运行单元测试...")
    
    # 发现并运行单元测试
    test_dir = project_root / "tests" / "unit"
    loader = unittest.TestLoader()
    suite = loader.discover(str(test_dir), pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_integration_tests():
    """运行集成测试"""
    print("\n运行集成测试...")
    
    # 发现并运行集成测试
    test_dir = project_root / "tests" / "integration"
    loader = unittest.TestLoader()
    suite = loader.discover(str(test_dir), pattern='test_*.py')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def main():
    """主函数"""
    print("开始运行测试...")
    
    # 检查是否有命令行参数
    test_type = sys.argv[1] if len(sys.argv) > 1 else "all"
    
    success = True
    
    if test_type in ["unit", "all"]:
        success &= run_unit_tests()
    
    if test_type in ["integration", "all"]:
        success &= run_integration_tests()
    
    if success:
        print("\n✅ 所有测试通过!")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
