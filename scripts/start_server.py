#!/usr/bin/env python3
"""
服务启动脚本
简化的服务启动入口
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.api.app import create_application

def main():
    """主函数"""
    # 获取配置文件路径
    config_file = None
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    
    try:
        # 创建应用程序
        print("正在初始化服务...")
        application = create_application(config_file)
        
        # 启动服务
        print("服务启动成功!")
        print(f"访问地址: http://localhost:5000")
        application.run()
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
