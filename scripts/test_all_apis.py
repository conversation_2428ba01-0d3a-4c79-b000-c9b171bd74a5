#!/usr/bin/env python3
"""
API接口全面测试脚本
测试所有API端点的功能和响应
"""

import sys
import os
import json
import time
import requests
import tempfile
from pathlib import Path
from PIL import Image
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class APITester:
    """API测试器类"""

    def __init__(self, base_url="http://localhost:5000"):
        """
        初始化API测试器

        Args:
            base_url: API服务的基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 30

        # 添加测试标识头，让服务器知道这是测试请求
        self.session.headers.update({
            'X-Test-Request': 'true',
            'User-Agent': 'AEC-API-Tester/1.0'
        })

        # 测试结果统计
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
    
    def log_test(self, test_name, success, message="", response_time=None):
        """记录测试结果"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            self.failed_tests += 1
            status = "❌ FAIL"
        
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "response_time": response_time
        }
        self.test_results.append(result)
        
        time_info = f" ({response_time:.2f}s)" if response_time else ""
        print(f"{status} {test_name}{time_info}")
        if message:
            print(f"    {message}")
    
    def test_health_check(self):
        """测试健康检查端点"""
        print("\n🔍 测试健康检查端点...")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/health")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if 'status' in data and 'model_loaded' in data:
                    self.log_test("健康检查", True, 
                                f"状态: {data.get('status')}, 模型已加载: {data.get('model_loaded')}", 
                                response_time)
                else:
                    self.log_test("健康检查", False, "响应格式不正确", response_time)
            else:
                self.log_test("健康检查", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_test("健康检查", False, f"请求异常: {str(e)}")
    
    def test_index_page(self):
        """测试主页"""
        print("\n🏠 测试主页...")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                if 'text/html' in response.headers.get('content-type', ''):
                    self.log_test("主页访问", True, "HTML页面正常返回", response_time)
                else:
                    self.log_test("主页访问", False, "返回内容不是HTML", response_time)
            else:
                self.log_test("主页访问", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_test("主页访问", False, f"请求异常: {str(e)}")
    
    def test_statistics_api(self):
        """测试统计API"""
        print("\n📊 测试统计API...")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/statistics")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                required_keys = ['success', 'api_statistics', 'timing_statistics']
                if all(key in data for key in required_keys):
                    self.log_test("统计API", True, 
                                f"返回数据完整，包含{len(data)}个字段", 
                                response_time)
                else:
                    missing_keys = [key for key in required_keys if key not in data]
                    self.log_test("统计API", False, 
                                f"缺少字段: {missing_keys}", response_time)
            else:
                self.log_test("统计API", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_test("统计API", False, f"请求异常: {str(e)}")
    
    def test_false_positive_reports_api(self):
        """测试误报记录API"""
        print("\n🔄 测试误报记录API...")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/api/false-positive/reports")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if 'success' in data and 'reports' in data and 'total_count' in data:
                    self.log_test("误报记录API", True, 
                                f"返回{data.get('total_count', 0)}条记录", 
                                response_time)
                else:
                    self.log_test("误报记录API", False, "响应格式不正确", response_time)
            else:
                self.log_test("误报记录API", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_test("误报记录API", False, f"请求异常: {str(e)}")
    
    def test_false_positive_viewer(self):
        """测试误报浏览器页面"""
        print("\n🖼️  测试误报浏览器...")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/false-positive-viewer")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                if 'text/html' in response.headers.get('content-type', ''):
                    self.log_test("误报浏览器", True, "页面正常返回", response_time)
                else:
                    self.log_test("误报浏览器", False, "返回内容不是HTML", response_time)
            else:
                self.log_test("误报浏览器", False, f"HTTP {response.status_code}", response_time)
                
        except Exception as e:
            self.log_test("误报浏览器", False, f"请求异常: {str(e)}")
    
    def test_predict_api_no_file(self):
        """测试推理API（无文件上传）"""
        print("\n🤖 测试推理API（无文件）...")
        
        try:
            start_time = time.time()
            response = self.session.post(f"{self.base_url}/predict")
            response_time = time.time() - start_time
            
            if response.status_code == 400:
                data = response.json()
                if 'error' in data:
                    self.log_test("推理API无文件测试", True, 
                                f"正确拒绝: {data.get('error')}", response_time)
                else:
                    self.log_test("推理API无文件测试", False, "错误响应格式不正确", response_time)
            else:
                self.log_test("推理API无文件测试", False, 
                            f"应返回400但返回{response.status_code}", response_time)
                
        except Exception as e:
            self.log_test("推理API无文件测试", False, f"请求异常: {str(e)}")
    
    def create_test_image(self):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = Image.new('RGB', (640, 480), color='red')
        
        # 添加一些简单的图形
        import PIL.ImageDraw as ImageDraw
        draw = ImageDraw.Draw(image)
        draw.rectangle([100, 100, 200, 200], fill='blue')
        draw.ellipse([300, 200, 400, 300], fill='green')
        
        return image
    
    def test_predict_api_with_file(self):
        """测试推理API（有文件上传）"""
        print("\n🤖 测试推理API（有文件）...")
        
        try:
            # 创建测试图像
            test_image = self.create_test_image()
            
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                test_image.save(temp_file.name, 'JPEG')
                temp_path = temp_file.name
            
            try:
                # 上传文件进行推理
                with open(temp_path, 'rb') as f:
                    files = {'image': ('test.jpg', f, 'image/jpeg')}
                    data = {'confidence': '0.1', 'return_image': 'false'}
                    
                    start_time = time.time()
                    response = self.session.post(f"{self.base_url}/predict", 
                                               files=files, data=data)
                    response_time = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    if 'success' in result and 'detections' in result:
                        detection_count = result.get('detections_count', 0)
                        self.log_test("推理API文件测试", True, 
                                    f"推理成功，检测到{detection_count}个对象", 
                                    response_time)
                    else:
                        self.log_test("推理API文件测试", False, "响应格式不正确", response_time)
                elif response.status_code == 503:
                    self.log_test("推理API文件测试", True, 
                                "模型未加载，返回503（正常情况）", response_time)
                else:
                    self.log_test("推理API文件测试", False, 
                                f"HTTP {response.status_code}", response_time)
                    
            finally:
                # 清理临时文件
                os.unlink(temp_path)
                
        except Exception as e:
            self.log_test("推理API文件测试", False, f"请求异常: {str(e)}")
    
    def test_false_positive_report_api(self):
        """测试误报上报API"""
        print("\n📝 测试误报上报API...")

        try:
            # 首先尝试提交一个误报上报（使用虚假的session_id）
            report_data = {
                "session_id": "test-session-id-12345",
                "false_positive_type": "false_detection",
                "reported_class_name": "test_class",
                "correct_class_name": "correct_class",
                "report_reason": "测试误报上报",
                "report_description": "这是一个测试用的误报上报",
                "reporter_info": "API测试脚本"
            }

            start_time = time.time()
            response = self.session.post(f"{self.base_url}/api/false-positive/report",
                                       json=report_data)
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                if 'success' in result and 'report_id' in result:
                    self.log_test("误报上报API", True,
                                f"上报成功，ID: {result.get('report_id')[:8]}...", response_time)
                else:
                    self.log_test("误报上报API", False, "响应格式不正确", response_time)
            elif response.status_code == 400:
                result = response.json()
                if 'error' in result:
                    self.log_test("误报上报API", True,
                                f"正确验证请求: {result.get('error')}", response_time)
                else:
                    self.log_test("误报上报API", False, "错误响应格式不正确", response_time)
            else:
                self.log_test("误报上报API", False, f"HTTP {response.status_code}", response_time)

        except Exception as e:
            self.log_test("误报上报API", False, f"请求异常: {str(e)}")

    def test_false_positive_package_api(self):
        """测试误报数据包创建API"""
        print("\n📦 测试误报数据包API...")

        try:
            data = {
                "status": "pending",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }

            start_time = time.time()
            response = self.session.post(f"{self.base_url}/api/false-positive/package",
                                       json=data)
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                if 'success' in result:
                    self.log_test("误报数据包API", True,
                                f"数据包创建成功", response_time)
                else:
                    self.log_test("误报数据包API", False, "响应格式不正确", response_time)
            elif response.status_code == 400:
                result = response.json()
                if 'error' in result:
                    self.log_test("误报数据包API", True,
                                f"正确处理无数据情况: {result.get('error')}", response_time)
                else:
                    self.log_test("误报数据包API", False, "错误响应格式不正确", response_time)
            else:
                self.log_test("误报数据包API", False, f"HTTP {response.status_code}", response_time)

        except Exception as e:
            self.log_test("误报数据包API", False, f"请求异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API接口全面测试...")
        print(f"目标服务: {self.base_url}")
        print("=" * 60)
        
        # 首先检查服务是否可达
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            print(f"✅ 服务可达 (HTTP {response.status_code})")
        except Exception as e:
            print(f"❌ 服务不可达: {str(e)}")
            print("请确保服务已启动并运行在正确的端口上")
            return False
        
        # 运行所有测试
        self.test_health_check()
        self.test_index_page()
        self.test_statistics_api()
        self.test_false_positive_reports_api()
        self.test_false_positive_viewer()
        self.test_predict_api_no_file()
        self.test_predict_api_with_file()
        self.test_false_positive_report_api()
        self.test_false_positive_package_api()
        
        # 显示测试结果
        self.show_results()
        
        return self.failed_tests == 0
    
    def show_results(self):
        """显示测试结果"""
        print("\n" + "=" * 60)
        print("📋 测试结果汇总")
        print("=" * 60)
        
        print(f"总测试数: {self.total_tests}")
        print(f"通过: {self.passed_tests}")
        print(f"失败: {self.failed_tests}")
        print(f"成功率: {(self.passed_tests/self.total_tests*100):.1f}%")
        
        if self.failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        print("\n" + "=" * 60)
        if self.failed_tests == 0:
            print("🎉 所有测试通过！API服务运行正常。")
        else:
            print(f"⚠️  有 {self.failed_tests} 个测试失败，请检查服务状态。")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="API接口全面测试")
    parser.add_argument("--url", default="http://localhost:5000",
                       help="API服务URL (默认: http://localhost:5000)")
    parser.add_argument("--json", action="store_true",
                       help="以JSON格式输出结果")
    parser.add_argument("--test-mode", action="store_true",
                       help="启用测试模式（设置环境变量AEC_TEST_MODE=true）")

    args = parser.parse_args()

    # 如果启用测试模式，设置环境变量
    if args.test_mode:
        import os
        os.environ['AEC_TEST_MODE'] = 'true'
        print("🧪 测试模式已启用 - 所有数据将标记为测试数据")
    
    # 创建测试器并运行测试
    tester = APITester(args.url)
    success = tester.run_all_tests()
    
    # 如果需要JSON输出
    if args.json:
        print("\n" + "=" * 60)
        print("JSON格式结果:")
        print(json.dumps({
            "total_tests": tester.total_tests,
            "passed_tests": tester.passed_tests,
            "failed_tests": tester.failed_tests,
            "success_rate": tester.passed_tests/tester.total_tests*100,
            "results": tester.test_results
        }, indent=2, ensure_ascii=False))
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
