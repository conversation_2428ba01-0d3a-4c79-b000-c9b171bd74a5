#!/usr/bin/env python3
"""
验证测试数据分离功能
用于验证测试请求是否正确标记为测试数据
"""

import sys
import os
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.core.database_manager import DatabaseManager
from src.core.config import get_config_manager


def test_data_separation():
    """测试数据分离功能"""
    print("🧪 验证测试数据分离功能")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 获取配置
    try:
        config_manager = get_config_manager()
        config = config_manager.get_config()
        
        # 创建数据库管理器（非测试模式，以便能看到所有数据）
        db_manager = DatabaseManager(
            db_path=config.database.path,
            schema_file=config.database.schema_file,
            test_mode=False
        )
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    # 获取初始统计
    print("📊 获取初始数据统计...")
    try:
        initial_stats = db_manager.get_test_data_statistics()
        initial_test_count = sum(stats['test_data'] for stats in initial_stats.values() if stats['test_data'] > 0)
        initial_prod_count = sum(stats['production_data'] for stats in initial_stats.values() if stats['production_data'] > 0)
        
        print(f"初始测试数据: {initial_test_count}")
        print(f"初始生产数据: {initial_prod_count}")
        
    except Exception as e:
        print(f"❌ 获取初始统计失败: {e}")
        return False
    
    # 测试1: 发送测试请求
    print("\n🔬 测试1: 发送带测试标识的请求...")
    try:
        test_headers = {
            'X-Test-Request': 'true',
            'User-Agent': 'AEC-Test-Verification/1.0'
        }
        
        # 发送健康检查请求
        response = requests.get(f"{base_url}/health", headers=test_headers, timeout=10)
        if response.status_code == 200:
            print("✅ 测试请求发送成功")
        else:
            print(f"❌ 测试请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 发送测试请求失败: {e}")
        return False
    
    # 测试2: 发送生产请求
    print("\n🏭 测试2: 发送普通生产请求...")
    try:
        prod_headers = {
            'User-Agent': 'Production-Client/1.0'
        }
        
        # 发送健康检查请求
        response = requests.get(f"{base_url}/health", headers=prod_headers, timeout=10)
        if response.status_code == 200:
            print("✅ 生产请求发送成功")
        else:
            print(f"❌ 生产请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 发送生产请求失败: {e}")
        return False
    
    # 等待数据写入
    print("\n⏳ 等待数据写入...")
    time.sleep(2)
    
    # 验证数据分离
    print("\n🔍 验证数据分离效果...")
    try:
        final_stats = db_manager.get_test_data_statistics()
        final_test_count = sum(stats['test_data'] for stats in final_stats.values() if stats['test_data'] > 0)
        final_prod_count = sum(stats['production_data'] for stats in final_stats.values() if stats['production_data'] > 0)
        
        print(f"最终测试数据: {final_test_count}")
        print(f"最终生产数据: {final_prod_count}")
        
        # 检查是否有新的测试数据
        test_data_increased = final_test_count > initial_test_count
        prod_data_increased = final_prod_count > initial_prod_count
        
        print("\n📋 验证结果:")
        if test_data_increased:
            print("✅ 测试数据正确增加")
        else:
            print("⚠️  测试数据未增加（可能API调用未产生数据库记录）")
        
        if prod_data_increased:
            print("✅ 生产数据正确增加")
        else:
            print("⚠️  生产数据未增加（可能API调用未产生数据库记录）")
        
        # 显示详细统计
        print("\n📊 详细统计:")
        for table, stats in final_stats.items():
            if stats['total'] > 0:
                print(f"{table:25} | 测试: {stats['test_data']:3} | 生产: {stats['production_data']:3} | 总计: {stats['total']:3}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证数据分离失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 测试数据分离验证工具")
    print("此工具验证测试请求是否正确标记为测试数据")
    print()
    
    # 检查服务是否运行
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code not in [200, 503]:
            print("❌ 服务未正常运行，请先启动服务")
            return 1
    except Exception:
        print("❌ 无法连接到服务，请确认服务已启动")
        return 1
    
    # 运行测试
    success = test_data_separation()
    
    if success:
        print("\n🎉 测试数据分离功能验证完成！")
        print("\n💡 使用建议:")
        print("- 运行测试脚本时，数据会自动标记为测试数据")
        print("- 正常使用API时，数据会标记为生产数据")
        print("- 使用 './run.sh test-data stats' 查看数据统计")
        print("- 使用 './run.sh test-data cleanup' 清理测试数据")
        return 0
    else:
        print("\n❌ 验证过程中出现问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
