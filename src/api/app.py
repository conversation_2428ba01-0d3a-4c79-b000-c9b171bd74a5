#!/usr/bin/env python3
"""
Flask应用主模块
创建和配置Flask应用
"""

import logging
from flask import Flask
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False

from ..core.config import get_config, get_config_manager
from ..core.database_manager import DatabaseManager
from ..core.image_storage import ImageStorage
from ..utils.logging_config import setup_logging
from ..services.inference_service import InferenceService
from ..services.statistics_service import StatisticsService
from ..services.false_positive_service import FalsePositiveService
from .routes import APIRoutes


class Application:
    """应用程序主类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化应用程序
        
        Args:
            config_file: 配置文件路径
        """
        # 初始化配置
        self.config_manager = get_config_manager(config_file)
        self.config = self.config_manager.get_config()
        
        # 设置日志
        self.logger = setup_logging()
        
        # 初始化组件
        self.app = None
        self.db_manager = None
        self.image_storage = None
        self.inference_service = None
        self.statistics_service = None
        self.false_positive_service = None
        self.api_routes = None
        
        self.logger.info("应用程序初始化开始")
    
    def create_app(self) -> Flask:
        """
        创建Flask应用
        
        Returns:
            Flask: 配置好的Flask应用实例
        """
        # 创建Flask应用
        self.app = Flask(__name__)
        
        # 配置Flask
        self._configure_flask()
        
        # 初始化存储系统
        if not self._initialize_storage_systems():
            raise RuntimeError("存储系统初始化失败")
        
        # 初始化服务
        if not self._initialize_services():
            raise RuntimeError("服务初始化失败")
        
        # 注册路由
        self._register_routes()
        
        self.logger.info("Flask应用创建完成")
        return self.app
    
    def _configure_flask(self):
        """配置Flask应用"""
        # 基本配置
        self.app.config['MAX_CONTENT_LENGTH'] = self.config.api.max_content_length_mb * 1024 * 1024
        self.app.config['DEBUG'] = self.config.api.debug
        
        # CORS配置
        if self.config.api.cors_enabled and CORS_AVAILABLE:
            CORS(self.app)
        elif self.config.api.cors_enabled and not CORS_AVAILABLE:
            self.logger.warning("CORS已启用但flask-cors未安装")
        
        self.logger.info("Flask应用配置完成")
    
    def _initialize_storage_systems(self) -> bool:
        """
        初始化数据库和图像存储系统
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化数据库管理器
            self.db_manager = DatabaseManager(
                db_path=self.config.database.path,
                schema_file=self.config.database.schema_file,
                test_mode=self.config.api.test_mode
            )
            self.logger.info("数据库管理器初始化成功")

            # 初始化图像存储管理器
            self.image_storage = ImageStorage(
                base_storage_path=self.config.storage.base_path
            )
            self.logger.info("图像存储管理器初始化成功")

            return True

        except Exception as e:
            self.logger.error(f"存储系统初始化失败: {e}")
            return False
    
    def _initialize_services(self) -> bool:
        """
        初始化各种服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化推理服务
            self.inference_service = InferenceService()
            if not self.inference_service.initialize():
                self.logger.error("推理服务初始化失败")
                return False
            self.logger.info("推理服务初始化成功")
            
            # 初始化统计服务
            self.statistics_service = StatisticsService(self.db_manager)
            self.logger.info("统计服务初始化成功")
            
            # 初始化误报处理服务
            self.false_positive_service = FalsePositiveService(
                self.db_manager, self.image_storage
            )
            self.logger.info("误报处理服务初始化成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            return False
    
    def _register_routes(self):
        """注册API路由"""
        self.api_routes = APIRoutes(
            self.app,
            self.inference_service,
            self.statistics_service,
            self.false_positive_service,
            self.db_manager,
            self.image_storage
        )
        self.logger.info("API路由注册完成")
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info(f"启动服务器: {self.config.api.host}:{self.config.api.port}")
            self.app.run(
                host=self.config.api.host,
                port=self.config.api.port,
                debug=self.config.api.debug
            )
        except Exception as e:
            self.logger.error(f"服务器启动失败: {e}")
            raise
    
    def get_app(self) -> Flask:
        """
        获取Flask应用实例
        
        Returns:
            Flask: Flask应用实例
        """
        return self.app
    
    def get_services(self) -> dict:
        """
        获取所有服务实例
        
        Returns:
            dict: 服务实例字典
        """
        return {
            'inference_service': self.inference_service,
            'statistics_service': self.statistics_service,
            'false_positive_service': self.false_positive_service,
            'db_manager': self.db_manager,
            'image_storage': self.image_storage
        }


def create_app(config_file: str = None) -> Flask:
    """
    应用工厂函数
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    application = Application(config_file)
    return application.create_app()


def create_application(config_file: str = None) -> Application:
    """
    创建应用程序实例
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        Application: 应用程序实例
    """
    application = Application(config_file)
    application.create_app()
    return application
