#!/usr/bin/env python3
"""
API路由模块
定义所有的Flask API路由
"""

import os
import json
import time
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional

from flask import Flask, request, jsonify, render_template_string, send_from_directory, abort

from ..core.config import get_config
from ..utils.logging_config import get_logger
from ..services.inference_service import InferenceService
from ..services.statistics_service import StatisticsService
from ..services.false_positive_service import FalsePositiveService


class APIRoutes:
    """API路由管理类"""
    
    def __init__(self, app: Flask, inference_service: InferenceService, 
                 statistics_service: StatisticsService, false_positive_service: FalsePositiveService,
                 db_manager=None, image_storage=None):
        """
        初始化API路由
        
        Args:
            app: Flask应用实例
            inference_service: 推理服务实例
            statistics_service: 统计服务实例
            false_positive_service: 误报处理服务实例
            db_manager: 数据库管理器实例
            image_storage: 图像存储管理器实例
        """
        self.app = app
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.inference_service = inference_service
        self.statistics_service = statistics_service
        self.false_positive_service = false_positive_service
        self.db_manager = db_manager
        self.image_storage = image_storage
        
        # 注册路由
        self.register_routes()

    def is_test_request(self):
        """检测是否为测试请求"""
        # 检查测试标识头
        if request.headers.get('X-Test-Request') == 'true':
            return True

        # 检查User-Agent是否包含测试标识
        user_agent = request.headers.get('User-Agent', '')
        if 'AEC-API-Tester' in user_agent or 'curl' in user_agent.lower():
            return True

        # 检查是否来自测试脚本（通过特定的User-Agent模式）
        test_patterns = ['test', 'pytest', 'unittest', 'postman']
        for pattern in test_patterns:
            if pattern in user_agent.lower():
                return True

        return False

    def register_routes(self):
        """注册所有路由"""
        # 主页路由
        self.app.add_url_rule('/', 'index', self.index, methods=['GET'])
        
        # API路由
        self.app.add_url_rule('/health', 'health_check', self.health_check, methods=['GET'])
        self.app.add_url_rule('/predict', 'predict', self.predict, methods=['POST'])
        
        # 误报相关路由
        self.app.add_url_rule('/false-positive-viewer', 'false_positive_viewer',
                             self.false_positive_viewer, methods=['GET'])
        self.app.add_url_rule('/api/false-positive/reports', 'get_false_positive_reports',
                             self.get_false_positive_reports, methods=['GET'])
        self.app.add_url_rule('/api/false-positive/report', 'submit_false_positive_report',
                             self.submit_false_positive_report, methods=['POST'])
        self.app.add_url_rule('/api/false-positive/package', 'create_false_positive_package',
                             self.create_false_positive_package, methods=['POST'])
        
        # 统计相关路由
        self.app.add_url_rule('/api/statistics', 'get_statistics', 
                             self.get_statistics, methods=['GET'])
    
    def index(self):
        """主页 - API调用统计"""
        try:
            # 获取统计信息
            api_stats = self.statistics_service.get_api_statistics()
            timing_stats = self.statistics_service.get_timing_statistics()
            false_positive_stats = self.statistics_service.get_false_positive_statistics()
            daily_stats = self.statistics_service.get_daily_statistics(days=15)
            
            # 生成HTML页面（这里简化处理，实际应该使用模板）
            html_content = self._generate_index_html(api_stats, timing_stats, 
                                                   false_positive_stats, daily_stats)
            
            return html_content
            
        except Exception as e:
            self.statistics_service.update_api_stats('index', False)
            self.logger.error(f"主页访问错误: {str(e)}")
            return f"服务器错误: {str(e)}", 500
    
    def health_check(self):
        """健康检查端点"""
        try:
            model_info = self.inference_service.get_model_info()
            
            status = {
                "status": "healthy" if model_info["model_loaded"] else "unhealthy",
                "model_loaded": model_info["model_loaded"],
                "model_path": model_info["model_path"],
                "timestamp": datetime.now().isoformat(),
                "supported_formats": model_info["supported_formats"]
            }

            return jsonify(status), 200 if model_info["model_loaded"] else 503

        except Exception as e:
            self.logger.error(f"健康检查错误: {str(e)}")
            return jsonify({
                "error": "健康检查失败",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500
    
    def predict(self):
        """图像推理端点"""
        total_start_time = time.time()
        
        try:
            # 检查模型是否已加载
            model_info = self.inference_service.get_model_info()
            if not model_info["model_loaded"]:
                self.statistics_service.update_api_stats('predict', False)
                return jsonify({
                    "error": "模型未加载",
                    "message": "YOLO模型未正确加载，请检查模型文件"
                }), 503

            # 检查是否有文件上传
            if 'image' not in request.files:
                self.statistics_service.update_api_stats('predict', False)
                return jsonify({
                    "error": "缺少图像文件",
                    "message": "请在请求中包含名为'image'的文件"
                }), 400

            file = request.files['image']
            if file.filename == '':
                self.statistics_service.update_api_stats('predict', False)
                return jsonify({
                    "error": "未选择文件",
                    "message": "请选择要上传的图像文件"
                }), 400

            # 检查文件格式
            if not self.inference_service.is_allowed_file(file.filename):
                self.statistics_service.update_api_stats('predict', False)
                return jsonify({
                    "error": "不支持的文件格式",
                    "message": f"支持的格式: {', '.join(model_info['supported_formats'])}",
                    "uploaded_format": file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else "unknown"
                }), 400

            # 获取参数
            confidence_threshold = float(request.form.get('confidence', model_info['default_confidence_threshold']))
            return_image = request.form.get('return_image', 'false').lower() == 'true'

            # 生成会话ID
            session_id = self.db_manager.generate_session_id() if self.db_manager else None

            # 获取客户端信息
            client_ip = request.remote_addr
            user_agent = request.headers.get('User-Agent', '')

            # 读取图像数据
            image_data = file.read()
            
            # 保存原始图像
            original_image_path = None
            if self.image_storage and session_id:
                original_save_info = self.image_storage.save_image_from_bytes(
                    image_data, session_id, file.filename, "original"
                )
                original_image_path = original_save_info['relative_path']

            # 执行推理
            inference_result = self.inference_service.predict(image_data, confidence_threshold)
            
            # 创建标注图像（如果需要）
            annotated_image_base64 = None
            annotated_image_path = None
            image_annotation_time = 0
            
            if return_image and inference_result['detections']:
                annotation_start = time.time()
                
                # 重新预处理图像以获取数组格式
                image_array, _ = self.inference_service.preprocess_image(image_data)
                annotated_image_base64 = self.inference_service.create_annotated_image_base64(
                    image_array, inference_result['detections']
                )
                
                # 保存标注图像
                if self.image_storage and session_id:
                    annotated_image_array = self.inference_service.create_annotated_image(
                        image_array, inference_result['detections']
                    )
                    annotated_save_info = self.image_storage.save_annotated_image(
                        annotated_image_array, session_id, file.filename
                    )
                    annotated_image_path = annotated_save_info['relative_path']
                
                annotation_end = time.time()
                image_annotation_time = round((annotation_end - annotation_start) * 1000, 2)

            # 计算总时间
            total_end_time = time.time()
            total_time = round((total_end_time - total_start_time) * 1000, 2)
            
            # 更新时间信息
            timing_info = inference_result['timing'].copy()
            timing_info['image_annotation_time'] = image_annotation_time
            timing_info['total_time'] = total_time

            # 保存推理记录到数据库
            if self.db_manager and session_id:
                try:
                    # 检测是否为测试请求
                    is_test = self.is_test_request()

                    self.db_manager.save_inference_record(
                        session_id=session_id,
                        filename=file.filename,
                        file_size=len(image_data),
                        image_width=inference_result['image_info']['width'],
                        image_height=inference_result['image_info']['height'],
                        model_path=inference_result['model_info']['model_path'],
                        confidence_threshold=confidence_threshold,
                        detections_count=len(inference_result['detections']),
                        inference_timestamp=datetime.now().isoformat(),
                        timing_info=timing_info,
                        original_image_path=original_image_path,
                        annotated_image_path=annotated_image_path,
                        has_annotated_image=annotated_image_base64 is not None,
                        client_ip=client_ip,
                        user_agent=user_agent,
                        is_test=is_test
                    )

                    # 保存检测结果
                    self.db_manager.save_detection_results(session_id, inference_result['detections'], is_test=is_test)
                        
                except Exception as e:
                    self.logger.error(f"保存推理记录失败: {e}")

            # 更新统计
            self.statistics_service.update_api_stats('predict', True)
            self.statistics_service.update_inference_timing_stats(timing_info)

            # 构建响应
            response_data = {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "session_id": session_id,
                "filename": file.filename,
                "detections": inference_result['detections'],
                "detections_count": len(inference_result['detections']),
                "timing": timing_info,
                "model_path": inference_result['model_info']['model_path'],
                "confidence_threshold": confidence_threshold
            }

            if annotated_image_base64:
                response_data["annotated_image"] = annotated_image_base64

            # 记录响应摘要
            summary = self._log_response_summary(response_data)
            self.logger.info(f"推理完成: {json.dumps(summary, ensure_ascii=False)}")

            return jsonify(response_data)

        except Exception as e:
            self.statistics_service.update_api_stats('predict', False)
            self.logger.error(f"推理请求失败: {str(e)}")
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            
            return jsonify({
                "error": "推理失败",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500

    def false_positive_viewer(self):
        """误报图像浏览器页面"""
        # 这里应该返回一个HTML页面，简化处理
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>误报图像浏览器</title>
            <meta charset="UTF-8">
        </head>
        <body>
            <h1>误报图像浏览器</h1>
            <p>此功能正在开发中...</p>
            <p><a href="/">返回主页</a></p>
        </body>
        </html>
        """

    def get_false_positive_reports(self):
        """获取误报上报记录API"""
        try:
            # 获取查询参数
            status = request.args.get('status')
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # 获取误报记录
            reports = self.false_positive_service.get_false_positive_reports(
                status=status, start_date=start_date, end_date=end_date
            )

            return jsonify({
                "success": True,
                "reports": reports,
                "total_count": len(reports),
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"获取误报记录失败: {str(e)}")
            return jsonify({
                "error": "获取误报记录失败",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500

    def submit_false_positive_report(self):
        """提交误报上报API"""
        try:
            # 获取请求数据
            data = request.get_json()
            if not data:
                return jsonify({
                    "error": "缺少请求数据",
                    "message": "请提供JSON格式的上报数据",
                    "timestamp": datetime.now().isoformat()
                }), 400

            # 验证必需字段
            required_fields = ['session_id', 'false_positive_type']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return jsonify({
                    "error": "缺少必需字段",
                    "message": f"缺少字段: {missing_fields}",
                    "timestamp": datetime.now().isoformat()
                }), 400

            # 验证误报类型
            valid_types = ['false_detection', 'wrong_class', 'low_confidence']
            if data['false_positive_type'] not in valid_types:
                return jsonify({
                    "error": "无效的误报类型",
                    "message": f"支持的类型: {valid_types}",
                    "timestamp": datetime.now().isoformat()
                }), 400

            # 提交误报上报
            if self.db_manager:
                try:
                    # 检测是否为测试请求
                    is_test = self.is_test_request()

                    report_id = self.db_manager.submit_false_positive_report(
                        session_id=data['session_id'],
                        false_positive_type=data['false_positive_type'],
                        reported_class_name=data.get('reported_class_name'),
                        correct_class_name=data.get('correct_class_name'),
                        report_reason=data.get('report_reason'),
                        report_description=data.get('report_description'),
                        reporter_info=data.get('reporter_info'),
                        is_test=is_test
                    )

                    return jsonify({
                        "success": True,
                        "report_id": report_id,
                        "message": "误报上报提交成功",
                        "timestamp": datetime.now().isoformat()
                    })

                except Exception as e:
                    self.logger.error(f"保存误报上报失败: {str(e)}")
                    return jsonify({
                        "error": "保存误报上报失败",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }), 500
            else:
                return jsonify({
                    "error": "数据库未初始化",
                    "message": "无法保存误报上报",
                    "timestamp": datetime.now().isoformat()
                }), 503

        except Exception as e:
            self.logger.error(f"提交误报上报失败: {str(e)}")
            return jsonify({
                "error": "提交误报上报失败",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500

    def create_false_positive_package(self):
        """创建误报数据包API"""
        try:
            # 获取请求参数
            data = request.get_json() or {}
            status = data.get('status')
            start_date = data.get('start_date')
            end_date = data.get('end_date')

            # 获取误报记录
            reports = self.false_positive_service.get_false_positive_reports(
                status=status, start_date=start_date, end_date=end_date
            )

            if not reports:
                return jsonify({
                    "error": "没有找到符合条件的误报记录",
                    "message": "请检查筛选条件",
                    "timestamp": datetime.now().isoformat()
                }), 400

            # 创建数据包
            zip_path = self.false_positive_service.create_package(reports)

            if zip_path:
                return jsonify({
                    "success": True,
                    "package_path": zip_path,
                    "reports_count": len(reports),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                return jsonify({
                    "error": "创建数据包失败",
                    "message": "请检查服务器日志获取详细信息",
                    "timestamp": datetime.now().isoformat()
                }), 500

        except Exception as e:
            self.logger.error(f"创建误报数据包失败: {str(e)}")
            return jsonify({
                "error": "创建数据包失败",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500

    def get_statistics(self):
        """获取统计信息API"""
        try:
            api_stats = self.statistics_service.get_api_statistics()
            timing_stats = self.statistics_service.get_timing_statistics()
            false_positive_stats = self.statistics_service.get_false_positive_statistics()
            daily_stats = self.statistics_service.get_daily_statistics()
            package_stats = self.false_positive_service.get_package_statistics()

            return jsonify({
                "success": True,
                "api_statistics": api_stats,
                "timing_statistics": timing_stats,
                "false_positive_statistics": false_positive_stats,
                "daily_statistics": daily_stats,
                "package_statistics": package_stats,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return jsonify({
                "error": "获取统计信息失败",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500

    def _generate_index_html(self, api_stats: Dict[str, Any], timing_stats: Dict[str, Any],
                           false_positive_stats: Dict[str, Any], daily_stats: list) -> str:
        """
        生成主页HTML内容

        Args:
            api_stats: API统计信息
            timing_stats: 时间统计信息
            false_positive_stats: 误报统计信息
            daily_stats: 每日统计数据

        Returns:
            str: HTML内容
        """
        # 简化的HTML模板
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>算法在线推理服务 - 统计面板</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }}
                .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }}
                .stat-card {{ background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #007bff; }}
                .stat-number {{ font-size: 2em; font-weight: bold; color: #007bff; }}
                h1 {{ color: #333; text-align: center; }}
                h2 {{ color: #007bff; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📊 算法在线推理服务 - 统计面板</h1>

                <h2>📈 总体统计</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>API调用次数</h3>
                        <div class="stat-number">{api_stats.get('total_calls', 0)}</div>
                        <p>成功率: {api_stats.get('overall_success_rate', 0)}%</p>
                    </div>
                    <div class="stat-card">
                        <h3>误报统计</h3>
                        <div class="stat-number">{false_positive_stats.get('total_false_positives', 0)}</div>
                        <p>误报率: {false_positive_stats.get('overall_false_positive_rate', 0)}%</p>
                    </div>
                    <div class="stat-card">
                        <h3>推理次数</h3>
                        <div class="stat-number">{timing_stats.get('total_inferences', 0)}</div>
                        <p>平均时间: {timing_stats.get('overall', {}).get('avg_total_time_ms', 0)} ms</p>
                    </div>
                </div>

                <h2>🔗 快速链接</h2>
                <div class="stat-card">
                    <p><a href="/health">🔍 健康检查</a></p>
                    <p><a href="/false-positive-viewer">🖼️ 误报浏览器</a></p>
                    <p><a href="/api/statistics">📊 API统计数据</a></p>
                </div>

                <p style="text-align: center; color: #666; margin-top: 30px;">
                    服务时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </p>
            </div>
        </body>
        </html>
        """

    def _log_response_summary(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        记录响应数据摘要，避免打印完整的base64数据

        Args:
            response_data: 响应数据

        Returns:
            Dict[str, Any]: 响应摘要
        """
        summary = {
            "success": response_data.get("success"),
            "timestamp": response_data.get("timestamp"),
            "filename": response_data.get("filename"),
            "detections_count": response_data.get("detections_count"),
            "timing": response_data.get("timing"),
            "model_path": response_data.get("model_path"),
            "confidence_threshold": response_data.get("confidence_threshold")
        }

        # 如果有标注图像，只记录其大小信息
        if "annotated_image" in response_data:
            image_data = response_data["annotated_image"]
            if image_data and image_data.startswith("data:image/"):
                # 计算base64数据大小
                base64_part = image_data.split(",", 1)[1] if "," in image_data else image_data
                size_kb = len(base64_part.encode('utf-8')) / 1024
                summary["annotated_image_info"] = {
                    "format": "base64_jpeg",
                    "size_kb": round(size_kb, 2)
                }

        return summary
