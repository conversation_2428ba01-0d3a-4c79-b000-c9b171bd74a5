#!/usr/bin/env python3
"""
配置管理模块
统一管理应用程序的配置参数，支持环境变量和配置文件
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field


@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "yolo_inference.db"
    schema_file: str = "database_schema.sql"
    backup_enabled: bool = True
    backup_interval_hours: int = 24


@dataclass
class ModelConfig:
    """模型配置"""
    model_path: str = "best.pt"
    confidence_threshold: float = 0.1
    warmup_enabled: bool = True
    warmup_image_path: str = "resource/images/test_image.jpg"


@dataclass
class StorageConfig:
    """存储配置"""
    base_path: str = "image_storage"
    max_file_size_mb: int = 16
    cleanup_enabled: bool = True
    cleanup_days: int = 30


@dataclass
class APIConfig:
    """API配置"""
    host: str = "0.0.0.0"
    port: int = 5000
    debug: bool = False
    max_content_length_mb: int = 16
    cors_enabled: bool = True
    test_mode: bool = False


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    log_dir: str = "logs"
    max_file_size_mb: int = 10
    backup_count: int = 10
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class StatisticsConfig:
    """统计配置"""
    enabled: bool = True
    auto_refresh_seconds: int = 30
    max_timing_records: int = 100


@dataclass
class FalsePositiveConfig:
    """误报处理配置"""
    package_output_dir: str = "false_positive_packages"
    auto_package_enabled: bool = False
    package_interval_days: int = 7


@dataclass
class AppConfig:
    """应用程序主配置"""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    api: APIConfig = field(default_factory=APIConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    statistics: StatisticsConfig = field(default_factory=StatisticsConfig)
    false_positive: FalsePositiveConfig = field(default_factory=FalsePositiveConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file
        self.config = AppConfig()
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 1. 首先加载默认配置
        self.config = AppConfig()
        
        # 2. 如果有配置文件，加载文件配置
        if self.config_file and Path(self.config_file).exists():
            self._load_from_file()
        
        # 3. 最后加载环境变量配置（优先级最高）
        self._load_from_env()
        
        # 4. 验证配置
        self._validate_config()
    
    def _load_from_file(self):
        """从配置文件加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 更新配置
            self._update_config_from_dict(file_config)
            self.logger.info(f"已加载配置文件: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            # 数据库配置
            'AEC_DB_PATH': ('database', 'path'),
            'AEC_DB_SCHEMA_FILE': ('database', 'schema_file'),
            
            # 模型配置
            'AEC_MODEL_PATH': ('model', 'model_path'),
            'AEC_CONFIDENCE_THRESHOLD': ('model', 'confidence_threshold'),
            
            # API配置
            'AEC_API_HOST': ('api', 'host'),
            'AEC_API_PORT': ('api', 'port'),
            'AEC_API_DEBUG': ('api', 'debug'),
            'AEC_TEST_MODE': ('api', 'test_mode'),
            
            # 存储配置
            'AEC_STORAGE_PATH': ('storage', 'base_path'),
            'AEC_MAX_FILE_SIZE_MB': ('storage', 'max_file_size_mb'),
            
            # 日志配置
            'AEC_LOG_LEVEL': ('logging', 'level'),
            'AEC_LOG_DIR': ('logging', 'log_dir'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                self._set_config_value(section, key, value)
    
    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for section_name, section_data in config_dict.items():
            if hasattr(self.config, section_name) and isinstance(section_data, dict):
                section = getattr(self.config, section_name)
                for key, value in section_data.items():
                    if hasattr(section, key):
                        setattr(section, key, value)
    
    def _set_config_value(self, section: str, key: str, value: str):
        """设置配置值（自动类型转换）"""
        if hasattr(self.config, section):
            section_obj = getattr(self.config, section)
            if hasattr(section_obj, key):
                # 获取原始类型并转换
                original_value = getattr(section_obj, key)
                if isinstance(original_value, bool):
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif isinstance(original_value, int):
                    value = int(value)
                elif isinstance(original_value, float):
                    value = float(value)
                
                setattr(section_obj, key, value)
    
    def _validate_config(self):
        """验证配置"""
        # 验证路径
        if not Path(self.config.database.schema_file).exists():
            self.logger.warning(f"数据库架构文件不存在: {self.config.database.schema_file}")
        
        # 验证端口范围
        if not (1 <= self.config.api.port <= 65535):
            raise ValueError(f"无效的端口号: {self.config.api.port}")
        
        # 验证日志级别
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.config.logging.level.upper() not in valid_levels:
            raise ValueError(f"无效的日志级别: {self.config.logging.level}")
    
    def get_config(self) -> AppConfig:
        """获取配置对象"""
        return self.config
    
    def save_config(self, output_file: str):
        """保存配置到文件"""
        try:
            config_dict = self._config_to_dict()
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置已保存到: {output_file}")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        result = {}
        for field_name in self.config.__dataclass_fields__:
            section = getattr(self.config, field_name)
            section_dict = {}
            for attr_name in section.__dataclass_fields__:
                section_dict[attr_name] = getattr(section, attr_name)
            result[field_name] = section_dict
        return result


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """获取配置管理器实例（单例模式）"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    return _config_manager


def get_config() -> AppConfig:
    """获取应用配置"""
    return get_config_manager().get_config()
