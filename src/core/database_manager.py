#!/usr/bin/env python3
"""
数据库管理模块
用于管理YOLO推理服务的SQLite数据库
提供数据库初始化、连接管理和CRUD操作功能
"""

import os
import sqlite3
import logging
import hashlib
import uuid
import time
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from contextlib import contextmanager

class DatabaseManager:
    """数据库管理器类"""

    def __init__(self, db_path: str = "yolo_inference.db", schema_file: str = "database_schema.sql", test_mode: bool = False):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
            schema_file: 数据库架构文件路径
            test_mode: 是否为测试模式，测试模式下所有数据都会标记为测试数据
        """
        self.db_path = Path(db_path)
        self.schema_file = Path(schema_file)
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)

        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 初始化数据库
        self.initialize_database()

        # 更新数据库架构（添加测试标识字段）
        self.update_database_schema()

    def initialize_database(self):
        """初始化数据库，创建表结构"""
        try:
            # 检查架构文件是否存在
            if not self.schema_file.exists():
                raise FileNotFoundError(f"数据库架构文件不存在: {self.schema_file}")

            # 读取架构文件
            with open(self.schema_file, 'r', encoding='utf-8') as f:
                schema_sql = f.read()

            # 执行架构创建
            with self.get_connection() as conn:
                conn.executescript(schema_sql)
                conn.commit()

            self.logger.info(f"数据库初始化成功: {self.db_path}")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise

    def update_database_schema(self):
        """更新数据库架构，添加测试标识字段"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查是否已经有is_test字段
                tables_to_update = [
                    'inference_records',
                    'detection_results',
                    'image_info',
                    'false_positive_reports',
                    'api_call_statistics',
                    'statistics_summary'
                ]

                for table_name in tables_to_update:
                    try:
                        # 检查表是否存在
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                        if not cursor.fetchone():
                            continue

                        # 检查is_test字段是否存在
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = [column[1] for column in cursor.fetchall()]

                        if 'is_test' not in columns:
                            # 添加is_test字段
                            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN is_test BOOLEAN DEFAULT FALSE")
                            self.logger.info(f"已为表 {table_name} 添加 is_test 字段")

                            # 创建索引
                            index_name = f"idx_{table_name}_is_test"
                            cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}(is_test)")

                    except Exception as e:
                        self.logger.warning(f"更新表 {table_name} 失败: {e}")
                        continue

                conn.commit()
                self.logger.info("数据库架构更新完成")

        except Exception as e:
            self.logger.error(f"数据库架构更新失败: {e}")
            # 不抛出异常，允许系统继续运行

    @contextmanager
    def get_connection(self, max_retries: int = 3, retry_delay: float = 0.1):
        """
        获取数据库连接的上下文管理器，支持重试机制

        Args:
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        conn = None
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                conn = sqlite3.connect(str(self.db_path), timeout=30.0)
                conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问

                # 设置一些优化参数
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA cache_size=10000")
                conn.execute("PRAGMA temp_store=MEMORY")

                yield conn
                return

            except sqlite3.OperationalError as e:
                last_error = e
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                    try:
                        conn.close()
                    except:
                        pass
                    conn = None

                if attempt < max_retries:
                    self.logger.warning(f"数据库连接失败，第{attempt + 1}次重试: {e}")
                    time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                else:
                    self.logger.error(f"数据库连接失败，已达到最大重试次数: {e}")
                    raise

            except Exception as e:
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                self.logger.error(f"数据库操作错误: {e}")
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass

    def generate_session_id(self) -> str:
        """生成唯一的会话ID"""
        return str(uuid.uuid4())

    def calculate_file_hash(self, file_data: bytes, algorithm: str = 'md5') -> str:
        """计算文件哈希值"""
        if algorithm == 'md5':
            return hashlib.md5(file_data).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(file_data).hexdigest()
        else:
            raise ValueError(f"不支持的哈希算法: {algorithm}")

    def save_inference_record(self,
                            session_id: str,
                            filename: str,
                            file_size: int,
                            image_width: int,
                            image_height: int,
                            model_path: str,
                            confidence_threshold: float,
                            detections_count: int,
                            inference_timestamp: str,
                            timing_info: Dict[str, float],
                            original_image_path: Optional[str] = None,
                            annotated_image_path: Optional[str] = None,
                            has_annotated_image: bool = False,
                            client_ip: Optional[str] = None,
                            user_agent: Optional[str] = None,
                            status: str = 'success',
                            error_message: Optional[str] = None,
                            is_test: Optional[bool] = None) -> int:
        """
        保存推理记录

        Args:
            is_test: 是否为测试数据，如果为None则使用实例的test_mode

        Returns:
            记录ID
        """
        try:
            # 确定是否为测试数据
            test_flag = is_test if is_test is not None else self.test_mode

            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO inference_records (
                    session_id, filename, file_size, image_width, image_height,
                    model_path, confidence_threshold, detections_count,
                    inference_timestamp, preprocess_time_ms, inference_time_ms,
                    postprocess_time_ms, image_annotation_time_ms, total_time_ms,
                    original_image_path, annotated_image_path, has_annotated_image,
                    client_ip, user_agent, status, error_message, is_test
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                cursor.execute(sql, (
                    session_id, filename, file_size, image_width, image_height,
                    model_path, confidence_threshold, detections_count,
                    inference_timestamp, timing_info.get('preprocess_time', 0),
                    timing_info.get('inference_time', 0), timing_info.get('postprocess_time', 0),
                    timing_info.get('image_annotation_time', 0), timing_info.get('total_time', 0),
                    original_image_path, annotated_image_path, has_annotated_image,
                    client_ip, user_agent, status, error_message, test_flag
                ))

                record_id = cursor.lastrowid
                conn.commit()

                self.logger.info(f"推理记录保存成功: session_id={session_id}, record_id={record_id}")
                return record_id

        except Exception as e:
            self.logger.error(f"保存推理记录失败: {e}")
            raise

    def save_detection_results(self, session_id: str, detections: List[Dict[str, Any]], is_test: Optional[bool] = None) -> List[int]:
        """
        保存检测结果

        Args:
            session_id: 会话ID
            detections: 检测结果列表
            is_test: 是否为测试数据，如果为None则使用实例的test_mode

        Returns:
            保存的记录ID列表
        """
        try:
            # 确定是否为测试数据
            test_flag = is_test if is_test is not None else self.test_mode

            record_ids = []

            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO detection_results (
                    session_id, detection_index, class_id, class_name, confidence,
                    bbox_x1, bbox_y1, bbox_x2, bbox_y2, bbox_center_x, bbox_center_y,
                    bbox_width, bbox_height, is_test
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                for i, detection in enumerate(detections):
                    bbox = detection['bbox']
                    cursor.execute(sql, (
                        session_id, i, detection['class_id'], detection['class_name'],
                        detection['confidence'], bbox['x1'], bbox['y1'], bbox['x2'], bbox['y2'],
                        bbox['center_x'], bbox['center_y'], bbox['width'], bbox['height'], test_flag
                    ))
                    record_ids.append(cursor.lastrowid)

                conn.commit()

                self.logger.info(f"检测结果保存成功: session_id={session_id}, count={len(detections)}")
                return record_ids

        except Exception as e:
            self.logger.error(f"保存检测结果失败: {e}")
            raise

    def save_image_info(self,
                       session_id: str,
                       image_type: str,
                       file_path: str,
                       file_name: str,
                       file_size: int,
                       file_format: str,
                       width: int,
                       height: int,
                       channels: int = 3,
                       file_data: Optional[bytes] = None) -> int:
        """
        保存图像信息

        Args:
            session_id: 会话ID
            image_type: 图像类型 (original, annotated)
            file_path: 文件路径
            file_name: 文件名
            file_size: 文件大小
            file_format: 文件格式
            width: 图像宽度
            height: 图像高度
            channels: 颜色通道数
            file_data: 文件数据(用于计算哈希)

        Returns:
            记录ID
        """
        try:
            # 计算哈希值
            md5_hash = None
            sha256_hash = None
            if file_data:
                md5_hash = self.calculate_file_hash(file_data, 'md5')
                sha256_hash = self.calculate_file_hash(file_data, 'sha256')

            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO image_info (
                    session_id, image_type, file_path, file_name, file_size,
                    file_format, width, height, channels, md5_hash, sha256_hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                cursor.execute(sql, (
                    session_id, image_type, file_path, file_name, file_size,
                    file_format, width, height, channels, md5_hash, sha256_hash
                ))

                record_id = cursor.lastrowid
                conn.commit()

                self.logger.info(f"图像信息保存成功: session_id={session_id}, type={image_type}")
                return record_id

        except Exception as e:
            self.logger.error(f"保存图像信息失败: {e}")
            raise

    def get_inference_record(self, session_id: str) -> Optional[Dict[str, Any]]:
        """根据会话ID获取推理记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM inference_records WHERE session_id = ?", (session_id,))
                row = cursor.fetchone()

                if row:
                    return dict(row)
                return None

        except Exception as e:
            self.logger.error(f"查询推理记录失败: {e}")
            raise

    def get_detection_results(self, session_id: str) -> List[Dict[str, Any]]:
        """根据会话ID获取检测结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM detection_results
                    WHERE session_id = ?
                    ORDER BY detection_index
                """, (session_id,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询检测结果失败: {e}")
            raise

    def get_image_info(self, session_id: str, image_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """根据会话ID获取图像信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if image_type:
                    cursor.execute("""
                        SELECT * FROM image_info
                        WHERE session_id = ? AND image_type = ?
                    """, (session_id, image_type))
                else:
                    cursor.execute("SELECT * FROM image_info WHERE session_id = ?", (session_id,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询图像信息失败: {e}")
            raise

    def get_recent_inferences(self, limit: int = 50, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取最近的推理记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if status:
                    cursor.execute("""
                        SELECT * FROM v_inference_details
                        WHERE status = ?
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (status, limit))
                else:
                    cursor.execute("""
                        SELECT * FROM v_inference_details
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (limit,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询最近推理记录失败: {e}")
            raise

    def get_daily_statistics(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取每日统计数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM v_daily_stats
                    ORDER BY date DESC
                    LIMIT ?
                """, (days,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询每日统计失败: {e}")
            raise

    def get_false_positive_statistics(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取每日误报统计数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as false_positive_count,
                        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_count,
                        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count
                    FROM false_positive_reports
                    WHERE created_at >= datetime('now', '-{} days')
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                """.format(days))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询误报统计失败: {e}")
            raise

    def get_combined_daily_statistics(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取每日综合统计数据（包含请求数和误报数）"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT 
                        COALESCE(ir.date, fpr.date) as date,
                        COALESCE(ir.total_inferences, 0) as request_count,
                        COALESCE(fpr.false_positive_count, 0) as false_positive_count,
                        CASE 
                            WHEN COALESCE(ir.total_inferences, 0) > 0 
                            THEN ROUND(CAST(COALESCE(fpr.false_positive_count, 0) AS FLOAT) / ir.total_inferences * 100, 2)
                            ELSE 0 
                        END as false_positive_rate
                    FROM (
                        SELECT 
                            DATE(created_at) as date,
                            COUNT(*) as total_inferences
                        FROM inference_records
                        WHERE created_at >= datetime('now', '-{} days')
                        GROUP BY DATE(created_at)
                    ) ir
                    FULL OUTER JOIN (
                        SELECT 
                            DATE(created_at) as date,
                            COUNT(*) as false_positive_count
                        FROM false_positive_reports
                        WHERE created_at >= datetime('now', '-{} days')
                        GROUP BY DATE(created_at)
                    ) fpr ON ir.date = fpr.date
                    ORDER BY date DESC
                """.format(days, days))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询综合统计失败: {e}")
            raise

    def update_api_call_statistics(self, endpoint: str, method: str, success: bool = True):
        """更新API调用统计"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 尝试更新现有记录
                cursor.execute("""
                    UPDATE api_call_statistics 
                    SET 
                        total_calls = total_calls + 1,
                        successful_calls = successful_calls + ?,
                        failed_calls = failed_calls + ?,
                        last_called_at = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE endpoint = ? AND method = ?
                """, (1 if success else 0, 0 if success else 1, endpoint, method))
                
                # 如果没有更新任何记录，则插入新记录
                if cursor.rowcount == 0:
                    cursor.execute("""
                        INSERT INTO api_call_statistics (
                            endpoint, method, total_calls, successful_calls, failed_calls, last_called_at
                        ) VALUES (?, ?, 1, ?, ?, CURRENT_TIMESTAMP)
                    """, (endpoint, method, 1 if success else 0, 0 if success else 1))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新API调用统计失败: {e}")
            raise

    def get_api_call_statistics(self) -> List[Dict[str, Any]]:
        """获取API调用统计"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT 
                        endpoint,
                        method,
                        total_calls,
                        successful_calls,
                        failed_calls,
                        last_called_at,
                        CASE 
                            WHEN total_calls > 0 
                            THEN ROUND(CAST(successful_calls AS FLOAT) / total_calls * 100, 2)
                            ELSE 0 
                        END as success_rate
                    FROM api_call_statistics
                    ORDER BY total_calls DESC
                """)
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"获取API调用统计失败: {e}")
            raise

    def get_total_api_calls(self) -> Dict[str, int]:
        """获取总API调用统计"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT 
                        SUM(total_calls) as total_calls,
                        SUM(successful_calls) as successful_calls,
                        SUM(failed_calls) as failed_calls
                    FROM api_call_statistics
                """)
                
                result = cursor.fetchone()
                if result and result[0] is not None:
                    return {
                        'total_calls': result[0],
                        'successful_calls': result[1],
                        'failed_calls': result[2]
                    }
                else:
                    return {
                        'total_calls': 0,
                        'successful_calls': 0,
                        'failed_calls': 0
                    }
                
        except Exception as e:
            self.logger.error(f"获取总API调用统计失败: {e}")
            return {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0
            }

    def get_class_statistics(self) -> List[Dict[str, Any]]:
        """获取类别统计数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM v_class_stats")

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询类别统计失败: {e}")
            raise

    def update_daily_statistics(self, target_date: Optional[date] = None):
        """更新每日统计数据"""
        if target_date is None:
            target_date = date.today()

        date_key = target_date.strftime('%Y-%m-%d')

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 计算统计数据
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_inferences,
                        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_inferences,
                        SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed_inferences,
                        SUM(detections_count) as total_detections,
                        COUNT(DISTINCT (
                            SELECT GROUP_CONCAT(DISTINCT dr.class_name)
                            FROM detection_results dr
                            WHERE dr.session_id = ir.session_id
                        )) as unique_classes,
                        AVG(inference_time_ms) as avg_inference_time_ms,
                        MIN(inference_time_ms) as min_inference_time_ms,
                        MAX(inference_time_ms) as max_inference_time_ms,
                        COUNT(CASE WHEN has_annotated_image THEN 1 END) as total_images_stored,
                        SUM(file_size) / 1024.0 / 1024.0 as total_storage_size_mb
                    FROM inference_records ir
                    WHERE DATE(ir.created_at) = ?
                """, (date_key,))

                stats = cursor.fetchone()

                if stats and stats[0] > 0:  # 如果有数据
                    # 插入或更新统计数据
                    cursor.execute("""
                        INSERT OR REPLACE INTO statistics_summary (
                            date_key, total_inferences, successful_inferences, failed_inferences,
                            total_detections, unique_classes, avg_inference_time_ms,
                            min_inference_time_ms, max_inference_time_ms, total_images_stored,
                            total_storage_size_mb, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    """, (date_key, *stats))

                    conn.commit()
                    self.logger.info(f"每日统计更新成功: {date_key}")

        except Exception as e:
            self.logger.error(f"更新每日统计失败: {e}")
            raise

    def cleanup_old_records(self, days_to_keep: int = 90) -> Tuple[int, int, int]:
        """
        清理旧记录

        Args:
            days_to_keep: 保留的天数

        Returns:
            (删除的推理记录数, 删除的检测结果数, 删除的图像信息数)
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取要删除的会话ID
                cursor.execute("""
                    SELECT session_id FROM inference_records
                    WHERE created_at < datetime('now', '-{} days')
                """.format(days_to_keep))

                old_sessions = [row[0] for row in cursor.fetchall()]

                if not old_sessions:
                    return 0, 0, 0

                # 删除相关记录
                placeholders = ','.join(['?'] * len(old_sessions))

                # 删除检测结果
                cursor.execute(f"""
                    DELETE FROM detection_results
                    WHERE session_id IN ({placeholders})
                """, old_sessions)
                deleted_detections = cursor.rowcount

                # 删除图像信息
                cursor.execute(f"""
                    DELETE FROM image_info
                    WHERE session_id IN ({placeholders})
                """, old_sessions)
                deleted_images = cursor.rowcount

                # 删除推理记录
                cursor.execute(f"""
                    DELETE FROM inference_records
                    WHERE session_id IN ({placeholders})
                """, old_sessions)
                deleted_inferences = cursor.rowcount

                conn.commit()

                self.logger.info(f"清理完成: 删除了 {deleted_inferences} 条推理记录, "
                               f"{deleted_detections} 条检测结果, {deleted_images} 条图像信息")

                return deleted_inferences, deleted_detections, deleted_images

        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
            raise

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                stats = {}

                # 表记录数统计
                tables = ['inference_records', 'detection_results', 'image_info', 'statistics_summary']
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[f"{table}_count"] = cursor.fetchone()[0]

                # 数据库文件大小
                if self.db_path.exists():
                    stats['database_size_mb'] = round(self.db_path.stat().st_size / 1024 / 1024, 2)
                else:
                    stats['database_size_mb'] = 0

                # 最早和最新记录时间
                cursor.execute("SELECT MIN(created_at), MAX(created_at) FROM inference_records")
                min_time, max_time = cursor.fetchone()
                stats['earliest_record'] = min_time
                stats['latest_record'] = max_time

                return stats

        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            raise

    def submit_false_positive_report(self,
                                   session_id: str,
                                   false_positive_type: str,
                                   reported_class_name: Optional[str] = None,
                                   correct_class_name: Optional[str] = None,
                                   report_reason: Optional[str] = None,
                                   report_description: Optional[str] = None,
                                   reporter_info: Optional[str] = None,
                                   is_test: Optional[bool] = None) -> str:
        """
        提交误报上报

        Args:
            session_id: 关联的推理会话ID
            false_positive_type: 误报类型 (false_detection, wrong_class, low_confidence)
            reported_class_name: 被误报的类别名称
            correct_class_name: 正确的类别名称
            report_reason: 上报原因
            report_description: 详细描述
            reporter_info: 上报人信息
            is_test: 是否为测试数据，如果为None则使用实例的test_mode

        Returns:
            str: 上报ID
        """
        try:
            # 确定是否为测试数据
            test_flag = is_test if is_test is not None else self.test_mode

            # 生成上报ID
            report_id = str(uuid.uuid4())

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查session_id是否存在
                cursor.execute("SELECT COUNT(*) FROM inference_records WHERE session_id = ?", (session_id,))
                if cursor.fetchone()[0] == 0:
                    raise ValueError(f"推理会话不存在: {session_id}")

                # 插入误报上报记录
                sql = """
                INSERT INTO false_positive_reports (
                    session_id, report_id, false_positive_type, reported_class_name,
                    correct_class_name, report_reason, report_description, reporter_info, is_test
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                cursor.execute(sql, (
                    session_id, report_id, false_positive_type, reported_class_name,
                    correct_class_name, report_reason, report_description, reporter_info, test_flag
                ))

                conn.commit()

                self.logger.info(f"误报上报提交成功: report_id={report_id}, session_id={session_id}")
                return report_id

        except Exception as e:
            self.logger.error(f"提交误报上报失败: {e}")
            raise

    def get_false_positive_reports(self,
                                 status: Optional[str] = None,
                                 start_date: Optional[str] = None,
                                 end_date: Optional[str] = None,
                                 limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取误报上报记录

        Args:
            status: 状态过滤 (pending, confirmed, rejected, processed)
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            limit: 返回记录数限制

        Returns:
            List[Dict[str, Any]]: 误报上报记录列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_conditions = []
                params = []

                if status:
                    where_conditions.append("fpr.status = ?")
                    params.append(status)

                if start_date:
                    where_conditions.append("DATE(fpr.created_at) >= ?")
                    params.append(start_date)

                if end_date:
                    where_conditions.append("DATE(fpr.created_at) <= ?")
                    params.append(end_date)

                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)

                # 查询误报上报记录及相关信息
                query = f"""
                    SELECT
                        fpr.*,
                        ir.filename,
                        ir.file_size,
                        ir.image_width,
                        ir.image_height,
                        ir.model_path,
                        ir.confidence_threshold,
                        ir.detections_count,
                        ir.inference_timestamp,
                        ir.original_image_path,
                        ir.annotated_image_path,
                        ir.has_annotated_image
                    FROM false_positive_reports fpr
                    LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
                    {where_clause}
                    ORDER BY fpr.created_at DESC
                    LIMIT ?
                """

                params.append(limit)
                cursor.execute(query, params)

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"查询误报上报记录失败: {e}")
            raise

    def update_false_positive_report_status(self, report_id: str, status: str,
                                          admin_notes: Optional[str] = None) -> bool:
        """
        更新误报上报状态

        Args:
            report_id: 上报ID
            status: 新状态 (pending, confirmed, rejected, processed)
            admin_notes: 管理员备注

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    UPDATE false_positive_reports
                    SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE report_id = ?
                """, (status, admin_notes, report_id))

                if cursor.rowcount > 0:
                    conn.commit()
                    self.logger.info(f"误报上报状态更新成功: report_id={report_id}, status={status}")
                    return True
                else:
                    self.logger.warning(f"未找到误报上报记录: report_id={report_id}")
                    return False

        except Exception as e:
            self.logger.error(f"更新误报上报状态失败: {e}")
            raise

    def cleanup_test_data(self) -> Dict[str, int]:
        """
        清理所有测试数据

        Returns:
            Dict[str, int]: 清理的记录数统计
        """
        try:
            cleanup_stats = {}

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 清理各个表的测试数据
                tables = [
                    'inference_records',
                    'detection_results',
                    'image_info',
                    'false_positive_reports',
                    'api_call_statistics',
                    'statistics_summary'
                ]

                for table in tables:
                    try:
                        # 统计要删除的记录数
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE is_test = TRUE")
                        count = cursor.fetchone()[0]

                        if count > 0:
                            # 删除测试数据
                            cursor.execute(f"DELETE FROM {table} WHERE is_test = TRUE")
                            cleanup_stats[table] = count
                            self.logger.info(f"已清理表 {table} 中的 {count} 条测试数据")
                        else:
                            cleanup_stats[table] = 0

                    except Exception as e:
                        self.logger.error(f"清理表 {table} 的测试数据失败: {e}")
                        cleanup_stats[table] = -1

                conn.commit()

                total_cleaned = sum(count for count in cleanup_stats.values() if count > 0)
                self.logger.info(f"测试数据清理完成，共清理 {total_cleaned} 条记录")

                return cleanup_stats

        except Exception as e:
            self.logger.error(f"清理测试数据失败: {e}")
            raise

    def get_test_data_statistics(self) -> Dict[str, Dict[str, int]]:
        """
        获取测试数据统计

        Returns:
            Dict[str, Dict[str, int]]: 各表的测试数据和生产数据统计
        """
        try:
            stats = {}

            with self.get_connection() as conn:
                cursor = conn.cursor()

                tables = [
                    'inference_records',
                    'detection_results',
                    'image_info',
                    'false_positive_reports',
                    'api_call_statistics',
                    'statistics_summary'
                ]

                for table in tables:
                    try:
                        # 统计测试数据
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE is_test = TRUE")
                        test_count = cursor.fetchone()[0]

                        # 统计生产数据
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE is_test = FALSE OR is_test IS NULL")
                        prod_count = cursor.fetchone()[0]

                        # 统计总数
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        total_count = cursor.fetchone()[0]

                        stats[table] = {
                            'test_data': test_count,
                            'production_data': prod_count,
                            'total': total_count
                        }

                    except Exception as e:
                        self.logger.error(f"获取表 {table} 统计失败: {e}")
                        stats[table] = {
                            'test_data': -1,
                            'production_data': -1,
                            'total': -1
                        }

                return stats

        except Exception as e:
            self.logger.error(f"获取测试数据统计失败: {e}")
            raise