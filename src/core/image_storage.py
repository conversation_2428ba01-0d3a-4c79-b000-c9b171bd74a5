#!/usr/bin/env python3
"""
图像存储管理模块
用于管理YOLO推理服务的图像文件存储
提供原始图像和标注图像的本地存储功能
"""

import os
import shutil
import logging
import tempfile
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import cv2
import numpy as np
from PIL import Image

class ImageStorage:
    """图像存储管理器类"""

    def __init__(self, base_storage_path: str = "image_storage"):
        """
        初始化图像存储管理器

        Args:
            base_storage_path: 基础存储路径
        """
        self.base_path = Path(base_storage_path)
        self.logger = logging.getLogger(__name__)

        # 线程锁，用于保证文件操作的线程安全
        self._lock = threading.Lock()

        # 创建存储目录结构
        self.setup_storage_directories()

    def setup_storage_directories(self):
        """设置存储目录结构"""
        try:
            # 创建主要目录
            directories = [
                self.base_path,
                self.base_path / "original",      # 原始图像
                self.base_path / "annotated",     # 标注图像
                self.base_path / "thumbnails",    # 缩略图
                self.base_path / "temp"           # 临时文件
            ]

            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)

            # 按日期创建子目录
            today = datetime.now().strftime('%Y/%m/%d')
            for img_type in ['original', 'annotated']:
                date_dir = self.base_path / img_type / today
                date_dir.mkdir(parents=True, exist_ok=True)

            self.logger.info(f"图像存储目录初始化成功: {self.base_path}")

        except Exception as e:
            self.logger.error(f"图像存储目录初始化失败: {e}")
            raise

    def generate_filename(self,
                         session_id: str,
                         original_filename: str,
                         image_type: str = "original",
                         timestamp: Optional[datetime] = None) -> str:
        """
        生成存储文件名

        Args:
            session_id: 会话ID
            original_filename: 原始文件名
            image_type: 图像类型 (original, annotated, thumbnail)
            timestamp: 时间戳

        Returns:
            生成的文件名
        """
        if timestamp is None:
            timestamp = datetime.now()

        # 获取文件扩展名
        file_ext = Path(original_filename).suffix.lower()
        if not file_ext:
            file_ext = '.jpg'  # 默认扩展名

        # 生成文件名: {timestamp}_{session_id}_{type}{ext}
        timestamp_str = timestamp.strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp_str}_{session_id[:8]}_{image_type}{file_ext}"

        return filename

    def get_storage_path(self,
                        filename: str,
                        image_type: str = "original",
                        timestamp: Optional[datetime] = None) -> Path:
        """
        获取存储路径

        Args:
            filename: 文件名
            image_type: 图像类型
            timestamp: 时间戳

        Returns:
            完整存储路径
        """
        if timestamp is None:
            timestamp = datetime.now()

        # 按日期组织目录
        date_path = timestamp.strftime('%Y/%m/%d')
        storage_path = self.base_path / image_type / date_path / filename

        # 确保目录存在
        storage_path.parent.mkdir(parents=True, exist_ok=True)

        return storage_path

    def save_image_from_bytes(self,
                             image_data: bytes,
                             session_id: str,
                             original_filename: str,
                             image_type: str = "original") -> Dict[str, any]:
        """
        从字节数据保存图像

        Args:
            image_data: 图像字节数据
            session_id: 会话ID
            original_filename: 原始文件名
            image_type: 图像类型

        Returns:
            保存信息字典
        """
        try:
            timestamp = datetime.now()

            # 生成文件名和路径
            filename = self.generate_filename(session_id, original_filename, image_type, timestamp)
            storage_path = self.get_storage_path(filename, image_type, timestamp)

            # 安全保存文件
            self._safe_save_file(storage_path, image_data)

            # 获取图像信息
            image_info = self.get_image_info(storage_path)

            save_info = {
                'filename': filename,
                'storage_path': str(storage_path),
                'relative_path': str(storage_path.relative_to(self.base_path)),
                'file_size': len(image_data),
                'timestamp': timestamp.isoformat(),
                'image_type': image_type,
                **image_info
            }

            self.logger.info(f"图像保存成功: {filename} ({image_type})")
            return save_info

        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
            raise

    def save_image_from_array(self,
                             image_array: np.ndarray,
                             session_id: str,
                             original_filename: str,
                             image_type: str = "annotated",
                             quality: int = 95) -> Dict[str, any]:
        """
        从numpy数组保存图像

        Args:
            image_array: 图像数组
            session_id: 会话ID
            original_filename: 原始文件名
            image_type: 图像类型
            quality: JPEG质量 (1-100)

        Returns:
            保存信息字典
        """
        try:
            timestamp = datetime.now()

            # 生成文件名和路径
            filename = self.generate_filename(session_id, original_filename, image_type, timestamp)
            storage_path = self.get_storage_path(filename, image_type, timestamp)

            # 安全保存图像
            self._safe_save_image_array(storage_path, image_array)

            # 获取文件大小和图像信息
            file_size = storage_path.stat().st_size
            image_info = self.get_image_info(storage_path)

            save_info = {
                'filename': filename,
                'storage_path': str(storage_path),
                'relative_path': str(storage_path.relative_to(self.base_path)),
                'file_size': file_size,
                'timestamp': timestamp.isoformat(),
                'image_type': image_type,
                **image_info
            }

            self.logger.info(f"图像保存成功: {filename} ({image_type})")
            return save_info

        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
            raise

    def get_image_info(self, image_path: Path) -> Dict[str, any]:
        """
        获取图像信息

        Args:
            image_path: 图像路径

        Returns:
            图像信息字典
        """
        try:
            # 使用PIL获取基本信息
            with Image.open(image_path) as img:
                width, height = img.size
                format_name = img.format or 'UNKNOWN'
                mode = img.mode

                # 计算通道数
                if mode == 'RGB':
                    channels = 3
                elif mode == 'RGBA':
                    channels = 4
                elif mode == 'L':
                    channels = 1
                else:
                    channels = 3  # 默认值

            return {
                'width': width,
                'height': height,
                'format': format_name.lower(),
                'channels': channels,
                'mode': mode
            }

        except Exception as e:
            self.logger.error(f"获取图像信息失败: {e}")
            # 返回默认值
            return {
                'width': 0,
                'height': 0,
                'format': 'unknown',
                'channels': 3,
                'mode': 'RGB'
            }

    def create_thumbnail(self,
                        source_path: Path,
                        session_id: str,
                        size: Tuple[int, int] = (200, 200)) -> Optional[Dict[str, any]]:
        """
        创建缩略图

        Args:
            source_path: 源图像路径
            session_id: 会话ID
            size: 缩略图尺寸

        Returns:
            缩略图信息字典
        """
        try:
            # 生成缩略图文件名
            original_filename = source_path.name
            thumbnail_filename = self.generate_filename(session_id, original_filename, "thumbnail")
            thumbnail_path = self.get_storage_path(thumbnail_filename, "thumbnails")

            # 创建缩略图
            with Image.open(source_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=85)

            # 获取缩略图信息
            file_size = thumbnail_path.stat().st_size
            image_info = self.get_image_info(thumbnail_path)

            thumbnail_info = {
                'filename': thumbnail_filename,
                'storage_path': str(thumbnail_path),
                'relative_path': str(thumbnail_path.relative_to(self.base_path)),
                'file_size': file_size,
                'image_type': 'thumbnail',
                **image_info
            }

            self.logger.info(f"缩略图创建成功: {thumbnail_filename}")
            return thumbnail_info

        except Exception as e:
            self.logger.error(f"创建缩略图失败: {e}")
            return None

    def get_image_path(self, relative_path: str) -> Path:
        """
        根据相对路径获取完整路径

        Args:
            relative_path: 相对路径

        Returns:
            完整路径
        """
        return self.base_path / relative_path

    def image_exists(self, relative_path: str) -> bool:
        """
        检查图像是否存在

        Args:
            relative_path: 相对路径

        Returns:
            是否存在
        """
        full_path = self.get_image_path(relative_path)
        return full_path.exists() and full_path.is_file()

    def delete_image(self, relative_path: str) -> bool:
        """
        删除图像文件

        Args:
            relative_path: 相对路径

        Returns:
            是否删除成功
        """
        try:
            full_path = self.get_image_path(relative_path)
            if full_path.exists():
                full_path.unlink()
                self.logger.info(f"图像删除成功: {relative_path}")
                return True
            else:
                self.logger.warning(f"图像文件不存在: {relative_path}")
                return False

        except Exception as e:
            self.logger.error(f"删除图像失败: {e}")
            return False

    def cleanup_old_images(self, days_to_keep: int = 90) -> Dict[str, int]:
        """
        清理旧图像文件

        Args:
            days_to_keep: 保留的天数

        Returns:
            清理统计信息
        """
        try:
            from datetime import timedelta

            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cleanup_stats = {
                'original_deleted': 0,
                'annotated_deleted': 0,
                'thumbnails_deleted': 0,
                'total_size_freed_mb': 0
            }

            # 遍历各类型目录
            for image_type in ['original', 'annotated', 'thumbnails']:
                type_dir = self.base_path / image_type
                if not type_dir.exists():
                    continue

                # 递归查找旧文件
                for file_path in type_dir.rglob('*'):
                    if file_path.is_file():
                        # 检查文件修改时间
                        file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                        if file_mtime < cutoff_date:
                            try:
                                file_size = file_path.stat().st_size
                                file_path.unlink()

                                cleanup_stats[f'{image_type}_deleted'] += 1
                                cleanup_stats['total_size_freed_mb'] += file_size / 1024 / 1024

                            except Exception as e:
                                self.logger.error(f"删除文件失败 {file_path}: {e}")

            # 清理空目录
            self._cleanup_empty_directories()

            cleanup_stats['total_size_freed_mb'] = round(cleanup_stats['total_size_freed_mb'], 2)

            self.logger.info(f"图像清理完成: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            self.logger.error(f"清理旧图像失败: {e}")
            raise

    def _cleanup_empty_directories(self):
        """清理空目录"""
        try:
            for image_type in ['original', 'annotated', 'thumbnails']:
                type_dir = self.base_path / image_type
                if not type_dir.exists():
                    continue

                # 从最深层开始清理空目录
                for dir_path in sorted(type_dir.rglob('*'), key=lambda p: len(p.parts), reverse=True):
                    if dir_path.is_dir() and not any(dir_path.iterdir()):
                        try:
                            dir_path.rmdir()
                            self.logger.debug(f"删除空目录: {dir_path}")
                        except Exception:
                            pass  # 忽略删除失败的情况

        except Exception as e:
            self.logger.error(f"清理空目录失败: {e}")

    def get_storage_statistics(self) -> Dict[str, any]:
        """
        获取存储统计信息

        Returns:
            存储统计信息字典
        """
        try:
            stats = {
                'total_files': 0,
                'total_size_mb': 0,
                'by_type': {}
            }

            # 统计各类型文件
            for image_type in ['original', 'annotated', 'thumbnails']:
                type_dir = self.base_path / image_type
                type_stats = {
                    'file_count': 0,
                    'size_mb': 0
                }

                if type_dir.exists():
                    for file_path in type_dir.rglob('*'):
                        if file_path.is_file():
                            file_size = file_path.stat().st_size
                            type_stats['file_count'] += 1
                            type_stats['size_mb'] += file_size / 1024 / 1024

                type_stats['size_mb'] = round(type_stats['size_mb'], 2)
                stats['by_type'][image_type] = type_stats

                stats['total_files'] += type_stats['file_count']
                stats['total_size_mb'] += type_stats['size_mb']

            stats['total_size_mb'] = round(stats['total_size_mb'], 2)

            # 添加存储路径信息
            stats['storage_path'] = str(self.base_path)
            stats['storage_exists'] = self.base_path.exists()

            return stats

        except Exception as e:
            self.logger.error(f"获取存储统计失败: {e}")
            raise

    def _safe_save_file(self, file_path: Path, data: bytes):
        """
        安全保存文件，使用原子操作避免文件损坏

        Args:
            file_path: 目标文件路径
            data: 要保存的数据
        """
        with self._lock:
            try:
                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)

                # 使用临时文件进行原子写入
                with tempfile.NamedTemporaryFile(
                    dir=file_path.parent,
                    prefix=f".tmp_{file_path.name}_",
                    delete=False
                ) as temp_file:
                    temp_file.write(data)
                    temp_file.flush()
                    os.fsync(temp_file.fileno())  # 强制写入磁盘
                    temp_path = temp_file.name

                # 原子性地移动临时文件到目标位置
                os.rename(temp_path, file_path)

            except Exception as e:
                # 清理临时文件
                try:
                    if 'temp_path' in locals() and os.path.exists(temp_path):
                        os.unlink(temp_path)
                except:
                    pass
                self.logger.error(f"安全保存文件失败 {file_path}: {e}")
                raise

    def _safe_save_image_array(self, file_path: Path, image_array: np.ndarray):
        """
        安全保存图像数组

        Args:
            file_path: 目标文件路径
            image_array: 图像数组
        """
        with self._lock:
            try:
                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)

                # 使用临时文件进行原子写入
                with tempfile.NamedTemporaryFile(
                    dir=file_path.parent,
                    prefix=f".tmp_{file_path.name}_",
                    suffix=file_path.suffix,
                    delete=False
                ) as temp_file:
                    temp_path = temp_file.name

                # 保存图像
                success = cv2.imwrite(temp_path, image_array)
                if not success:
                    raise RuntimeError("cv2.imwrite 保存失败")

                # 原子性地移动临时文件到目标位置
                os.rename(temp_path, file_path)

            except Exception as e:
                # 清理临时文件
                try:
                    if 'temp_path' in locals() and os.path.exists(temp_path):
                        os.unlink(temp_path)
                except:
                    pass
                self.logger.error(f"安全保存图像数组失败 {file_path}: {e}")
                raise