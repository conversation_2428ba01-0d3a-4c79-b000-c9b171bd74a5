#!/usr/bin/env python3
"""
误报处理服务模块
提供误报数据收集、打包和管理功能
"""

import os
import json
import zipfile
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from ..core.config import get_config
from ..utils.logging_config import get_logger


class FalsePositiveService:
    """误报处理服务类"""
    
    def __init__(self, db_manager=None, image_storage=None):
        """
        初始化误报处理服务
        
        Args:
            db_manager: 数据库管理器实例
            image_storage: 图像存储管理器实例
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.db_manager = db_manager
        self.image_storage = image_storage
    
    def get_false_positive_reports(self, status: Optional[str] = None, 
                                 start_date: Optional[str] = None, 
                                 end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取误报上报记录
        
        Args:
            status: 状态过滤 (pending, confirmed, rejected, processed)
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            List[Dict[str, Any]]: 误报上报记录列表
        """
        if not self.db_manager:
            self.logger.error("数据库管理器未初始化")
            return []
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = []
                params = []
                
                if status:
                    where_conditions.append("fpr.status = ?")
                    params.append(status)
                    
                if start_date:
                    where_conditions.append("DATE(fpr.created_at) >= ?")
                    params.append(start_date)
                    
                if end_date:
                    where_conditions.append("DATE(fpr.created_at) <= ?")
                    params.append(end_date)
                
                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                
                # 查询误报上报记录及相关信息
                query = f"""
                    SELECT 
                        fpr.*,
                        ir.filename,
                        ir.file_size,
                        ir.image_width,
                        ir.image_height,
                        ir.model_path,
                        ir.confidence_threshold,
                        ir.detections_count,
                        ir.inference_timestamp,
                        ir.original_image_path,
                        ir.annotated_image_path,
                        ir.has_annotated_image
                    FROM false_positive_reports fpr
                    LEFT JOIN inference_records ir ON fpr.session_id = ir.session_id
                    {where_clause}
                    ORDER BY fpr.created_at DESC
                """
                
                cursor.execute(query, params)
                reports = [dict(row) for row in cursor.fetchall()]
                
                self.logger.info(f"找到 {len(reports)} 条误报上报记录")
                return reports
                
        except Exception as e:
            self.logger.error(f"查询误报上报记录失败: {e}")
            return []
    
    def get_detection_results(self, session_id: str) -> List[Dict[str, Any]]:
        """
        获取特定会话的检测结果
        
        Args:
            session_id: 会话ID
            
        Returns:
            List[Dict[str, Any]]: 检测结果列表
        """
        if not self.db_manager:
            return []
        
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM detection_results 
                    WHERE session_id = ?
                    ORDER BY detection_index
                """, (session_id,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"查询检测结果失败: {e}")
            return []
    
    def copy_image_to_package(self, image_path: str, package_dir: str, new_filename: str) -> bool:
        """
        复制图像到打包目录
        
        Args:
            image_path: 原始图像路径
            package_dir: 打包目录
            new_filename: 新文件名
            
        Returns:
            bool: 是否成功复制
        """
        if not self.image_storage:
            return False
        
        try:
            if not image_path or not self.image_storage.image_exists(image_path):
                return False
                
            full_source_path = self.image_storage.get_image_path(image_path)
            target_path = os.path.join(package_dir, new_filename)
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            
            # 复制文件
            shutil.copy2(full_source_path, target_path)
            return True
            
        except Exception as e:
            self.logger.error(f"复制图像失败 {image_path}: {e}")
            return False
    
    def create_package(self, reports: List[Dict[str, Any]], 
                      output_dir: Optional[str] = None) -> Optional[str]:
        """
        创建误报数据包
        
        Args:
            reports: 误报上报记录列表
            output_dir: 输出目录，如果为None则使用配置中的默认目录
            
        Returns:
            Optional[str]: 生成的ZIP文件路径，失败时返回None
        """
        if not reports:
            self.logger.warning("没有找到误报上报记录，无法创建数据包")
            return None
        
        if output_dir is None:
            output_dir = self.config.false_positive.package_output_dir
            
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # 生成包名称
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            package_name = f"false_positive_data_{timestamp}"
            package_dir = output_path / package_name
            package_dir.mkdir(exist_ok=True)
            
            # 创建子目录
            images_dir = package_dir / "images"
            images_dir.mkdir(exist_ok=True)
            
            metadata_dir = package_dir / "metadata"
            metadata_dir.mkdir(exist_ok=True)
            
            # 处理每个误报上报记录
            package_summary = {
                "created_at": datetime.now().isoformat(),
                "total_reports": len(reports),
                "reports": []
            }
            
            for i, report in enumerate(reports):
                self.logger.info(f"处理误报上报 {i+1}/{len(reports)}: {report['report_id']}")
                
                session_id = report['session_id']
                report_id = report['report_id']
                
                # 创建报告专用目录
                report_dir = images_dir / f"report_{report_id}"
                report_dir.mkdir(exist_ok=True)
                
                # 复制原始图像
                original_copied = False
                if report['original_image_path']:
                    original_filename = f"original_{session_id}.jpg"
                    original_copied = self.copy_image_to_package(
                        report['original_image_path'],
                        str(report_dir),
                        original_filename
                    )
                
                # 复制标注图像
                annotated_copied = False
                if report['annotated_image_path'] and report['has_annotated_image']:
                    annotated_filename = f"annotated_{session_id}.jpg"
                    annotated_copied = self.copy_image_to_package(
                        report['annotated_image_path'],
                        str(report_dir),
                        annotated_filename
                    )
                
                # 获取检测结果
                detection_results = self.get_detection_results(session_id)
                
                # 创建报告元数据
                report_metadata = {
                    "report_info": {
                        "report_id": report_id,
                        "session_id": session_id,
                        "false_positive_type": report['false_positive_type'],
                        "reported_class_name": report['reported_class_name'],
                        "correct_class_name": report['correct_class_name'],
                        "report_reason": report['report_reason'],
                        "report_description": report['report_description'],
                        "reporter_info": report['reporter_info'],
                        "status": report['status'],
                        "created_at": report['created_at']
                    },
                    "inference_info": {
                        "filename": report['filename'],
                        "file_size": report['file_size'],
                        "image_dimensions": {
                            "width": report['image_width'],
                            "height": report['image_height']
                        },
                        "model_path": report['model_path'],
                        "confidence_threshold": report['confidence_threshold'],
                        "detections_count": report['detections_count'],
                        "inference_timestamp": report['inference_timestamp']
                    },
                    "detection_results": detection_results,
                    "files": {
                        "original_image": original_filename if original_copied else None,
                        "annotated_image": annotated_filename if annotated_copied else None
                    }
                }
                
                # 保存报告元数据
                metadata_file = metadata_dir / f"report_{report_id}.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(report_metadata, f, ensure_ascii=False, indent=2)
                
                # 添加到包摘要
                package_summary["reports"].append({
                    "report_id": report_id,
                    "session_id": session_id,
                    "false_positive_type": report['false_positive_type'],
                    "has_original_image": original_copied,
                    "has_annotated_image": annotated_copied,
                    "detections_count": len(detection_results)
                })
            
            # 保存包摘要
            summary_file = package_dir / "package_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(package_summary, f, ensure_ascii=False, indent=2)
            
            # 创建README文件
            readme_content = self._create_readme_content(package_name, package_summary)
            readme_file = package_dir / "README.md"
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            # 创建ZIP文件
            zip_path = output_path / f"{package_name}.zip"
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in package_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(package_dir)
                        zipf.write(file_path, arcname)
            
            # 删除临时目录
            shutil.rmtree(package_dir)
            
            self.logger.info(f"误报数据包创建成功: {zip_path}")
            self.logger.info(f"包大小: {zip_path.stat().st_size / (1024*1024):.2f} MB")
            
            return str(zip_path)
            
        except Exception as e:
            self.logger.error(f"创建数据包失败: {e}")
            return None

    def _create_readme_content(self, package_name: str, package_summary: Dict[str, Any]) -> str:
        """
        创建README文件内容

        Args:
            package_name: 包名称
            package_summary: 包摘要信息

        Returns:
            str: README文件内容
        """
        return f"""# 误报数据包

## 包信息
- 创建时间: {package_summary['created_at']}
- 误报上报数量: {package_summary['total_reports']}
- 包名称: {package_name}

## 目录结构
```
{package_name}/
├── images/                     # 图像文件
│   └── report_<report_id>/     # 每个报告的图像
│       ├── original_<session_id>.jpg    # 原始图像
│       └── annotated_<session_id>.jpg   # 标注图像
├── metadata/                   # 元数据文件
│   └── report_<report_id>.json # 每个报告的详细信息
├── package_summary.json        # 包摘要信息
└── README.md                   # 说明文档
```

## 使用说明
1. images/ 目录包含所有相关的图像文件
2. metadata/ 目录包含每个误报上报的详细信息，包括推理结果和检测数据
3. package_summary.json 提供整个包的概览信息

## 误报类型说明
- false_detection: 误检测（检测到不存在的对象）
- wrong_class: 错误分类（对象存在但分类错误）
- low_confidence: 低置信度（检测正确但置信度过低）

## 数据格式说明
每个报告的元数据文件包含以下信息：
- report_info: 误报上报的基本信息
- inference_info: 推理时的参数和结果信息
- detection_results: 详细的检测结果数据
- files: 相关的图像文件信息
"""

    def get_package_statistics(self) -> Dict[str, Any]:
        """
        获取打包统计信息

        Returns:
            Dict[str, Any]: 打包统计信息
        """
        try:
            output_dir = Path(self.config.false_positive.package_output_dir)
            if not output_dir.exists():
                return {
                    'total_packages': 0,
                    'total_size_mb': 0,
                    'packages': []
                }

            packages = []
            total_size = 0

            for zip_file in output_dir.glob("*.zip"):
                file_size = zip_file.stat().st_size
                total_size += file_size

                packages.append({
                    'filename': zip_file.name,
                    'created_at': datetime.fromtimestamp(zip_file.stat().st_ctime).isoformat(),
                    'size_mb': round(file_size / (1024 * 1024), 2)
                })

            # 按创建时间排序
            packages.sort(key=lambda x: x['created_at'], reverse=True)

            return {
                'total_packages': len(packages),
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'packages': packages
            }

        except Exception as e:
            self.logger.error(f"获取打包统计失败: {e}")
            return {
                'total_packages': 0,
                'total_size_mb': 0,
                'packages': []
            }

    def cleanup_old_packages(self, days: int = 30) -> int:
        """
        清理旧的数据包

        Args:
            days: 保留天数

        Returns:
            int: 清理的文件数量
        """
        try:
            output_dir = Path(self.config.false_positive.package_output_dir)
            if not output_dir.exists():
                return 0

            cutoff_time = datetime.now() - timedelta(days=days)
            cleaned_count = 0

            for zip_file in output_dir.glob("*.zip"):
                file_time = datetime.fromtimestamp(zip_file.stat().st_ctime)
                if file_time < cutoff_time:
                    zip_file.unlink()
                    cleaned_count += 1
                    self.logger.info(f"已清理旧数据包: {zip_file.name}")

            return cleaned_count

        except Exception as e:
            self.logger.error(f"清理旧数据包失败: {e}")
            return 0
