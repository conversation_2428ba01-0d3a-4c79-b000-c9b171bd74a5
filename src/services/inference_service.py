#!/usr/bin/env python3
"""
YOLO推理服务模块
提供图像推理、模型管理和结果处理功能
"""

import os
import io
import time
import logging
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import base64

import cv2
import numpy as np
from PIL import Image
from ultralytics import YOLO

from ..core.config import get_config
from ..utils.logging_config import get_logger


class InferenceService:
    """YOLO推理服务类"""
    
    def __init__(self):
        """初始化推理服务"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.model: Optional[YOLO] = None
        self.model_path: Optional[str] = None
        self.is_warmed_up = False
        
        # 支持的图像格式
        self.allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp'}
    
    def initialize(self) -> bool:
        """
        初始化推理服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载模型
            if not self.load_model():
                return False
            
            # 模型预热
            if self.config.model.warmup_enabled:
                self.warmup_model()
            
            self.logger.info("推理服务初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"推理服务初始化失败: {e}")
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return False
    
    def load_model(self) -> bool:
        """
        加载YOLO模型
        
        Returns:
            bool: 模型加载是否成功
        """
        try:
            model_path = self.find_model_file()
            if not model_path:
                self.logger.error("未找到YOLO模型文件！请确保模型文件存在。")
                return False

            self.logger.info(f"正在加载模型: {model_path}")
            self.model = YOLO(model_path)
            self.model_path = model_path

            model_info = {
                "model_path": model_path,
                "model_type": "YOLO",
                "status": "loaded_successfully"
            }
            self.logger.info(f"模型加载成功: {model_info}")
            return True

        except Exception as e:
            error_info = {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "model_path": self.model_path,
                "status": "load_failed"
            }
            self.logger.error(f"模型加载失败: {error_info}")
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return False
    
    def find_model_file(self) -> Optional[str]:
        """
        自动查找可用的YOLO模型文件
        
        Returns:
            str: 模型文件路径，如果未找到则返回None
        """
        # 首先检查配置中指定的模型路径
        if os.path.exists(self.config.model.model_path):
            return self.config.model.model_path
        
        # 搜索常见的模型文件位置
        search_paths = [
            "best.pt",
            "yolo.pt",
            "model.pt",
            "../weights/PACK_AOI_best.pt",
            "weights/best.pt",
            "models/best.pt"
        ]
        
        for path in search_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def warmup_model(self) -> bool:
        """
        模型预热 - 使用测试图片进行一次推理
        
        Returns:
            bool: 预热是否成功
        """
        if self.model is None:
            self.logger.warning("模型未加载，跳过预热")
            return False

        try:
            # 测试图片路径
            test_image_path = self.config.model.warmup_image_path

            if not os.path.exists(test_image_path):
                self.logger.warning(f"测试图片不存在: {test_image_path}，跳过模型预热")
                return False

            self.logger.info("开始模型预热...")
            warmup_start = time.time()

            # 读取测试图片
            image = Image.open(test_image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            image_array = np.array(image)

            # 执行推理（预热）
            results = self.model(image_array)

            # 简单处理结果以确保完整的推理流程
            detections = self.postprocess_results(results, self.config.model.confidence_threshold)

            warmup_end = time.time()
            warmup_time = round((warmup_end - warmup_start) * 1000, 2)

            warmup_info = {
                "test_image": test_image_path,
                "image_size": {"width": image.width, "height": image.height},
                "detections_count": len(detections),
                "warmup_time_ms": warmup_time,
                "status": "completed"
            }

            self.logger.info(f"模型预热完成: {warmup_info}")
            self.is_warmed_up = True
            return True

        except Exception as e:
            self.logger.error(f"模型预热失败: {str(e)}")
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            return False
    
    def is_allowed_file(self, filename: str) -> bool:
        """
        检查文件扩展名是否被支持
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否支持该文件格式
        """
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def preprocess_image(self, image_data: bytes) -> Tuple[np.ndarray, Image.Image]:
        """
        预处理上传的图像数据
        
        Args:
            image_data: 图像字节数据
            
        Returns:
            Tuple[np.ndarray, Image.Image]: 处理后的图像数组和PIL图像对象
            
        Raises:
            Exception: 图像预处理失败时抛出异常
        """
        try:
            # 将字节数据转换为PIL图像
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为RGB格式（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 转换为numpy数组
            image_array = np.array(image)
            
            return image_array, image
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {str(e)}")
            raise
    
    def predict(self, image_data: bytes, confidence_threshold: Optional[float] = None) -> Dict[str, Any]:
        """
        执行图像推理
        
        Args:
            image_data: 图像字节数据
            confidence_threshold: 置信度阈值，如果为None则使用配置中的默认值
            
        Returns:
            Dict[str, Any]: 推理结果，包含检测结果和时间信息
            
        Raises:
            Exception: 推理失败时抛出异常
        """
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        if confidence_threshold is None:
            confidence_threshold = self.config.model.confidence_threshold
        
        # 记录时间
        timing_info = {}
        total_start = time.time()
        
        try:
            # 预处理图像
            preprocess_start = time.time()
            image_array, pil_image = self.preprocess_image(image_data)
            preprocess_end = time.time()
            timing_info['preprocess_time'] = round((preprocess_end - preprocess_start) * 1000, 2)
            
            # 执行推理
            inference_start = time.time()
            results = self.model(pil_image)
            inference_end = time.time()
            timing_info['inference_time'] = round((inference_end - inference_start) * 1000, 2)
            
            # 后处理结果
            postprocess_start = time.time()
            detections = self.postprocess_results(results, confidence_threshold)
            postprocess_end = time.time()
            timing_info['postprocess_time'] = round((postprocess_end - postprocess_start) * 1000, 2)
            
            # 计算总时间
            total_end = time.time()
            timing_info['total_time'] = round((total_end - total_start) * 1000, 2)
            
            return {
                'detections': detections,
                'timing': timing_info,
                'image_info': {
                    'width': pil_image.width,
                    'height': pil_image.height,
                    'mode': pil_image.mode
                },
                'model_info': {
                    'model_path': self.model_path,
                    'confidence_threshold': confidence_threshold
                }
            }
            
        except Exception as e:
            self.logger.error(f"图像推理失败: {str(e)}")
            raise

    def postprocess_results(self, results, confidence_threshold: float = 0.1) -> List[Dict[str, Any]]:
        """
        后处理YOLO推理结果

        Args:
            results: YOLO推理结果
            confidence_threshold: 置信度阈值

        Returns:
            List[Dict[str, Any]]: 处理后的检测结果列表

        Raises:
            Exception: 结果后处理失败时抛出异常
        """
        try:
            processed_results = []

            for result in results:
                # 获取检测框信息
                boxes = result.boxes
                if boxes is not None:
                    for i, box in enumerate(boxes):
                        # 获取置信度
                        confidence = float(box.conf[0])

                        # 过滤低置信度检测
                        if confidence < confidence_threshold:
                            continue

                        # 获取边界框坐标 (x1, y1, x2, y2)
                        x1, y1, x2, y2 = box.xyxy[0].tolist()

                        # 获取类别ID和名称
                        class_id = int(box.cls[0])
                        class_name = result.names[class_id] if result.names else f"class_{class_id}"

                        # 计算中心点和尺寸
                        center_x = (x1 + x2) / 2
                        center_y = (y1 + y2) / 2
                        width = x2 - x1
                        height = y2 - y1

                        detection = {
                            "class_id": class_id,
                            "class_name": class_name,
                            "confidence": round(confidence, 4),
                            "bbox": {
                                "x1": round(x1, 2),
                                "y1": round(y1, 2),
                                "x2": round(x2, 2),
                                "y2": round(y2, 2),
                                "center_x": round(center_x, 2),
                                "center_y": round(center_y, 2),
                                "width": round(width, 2),
                                "height": round(height, 2)
                            }
                        }
                        processed_results.append(detection)

            return processed_results

        except Exception as e:
            self.logger.error(f"结果后处理失败: {str(e)}")
            raise

    def create_annotated_image(self, image_array: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        在图像上绘制检测结果

        Args:
            image_array: 原始图像数组
            detections: 检测结果列表

        Returns:
            np.ndarray: 标注后的图像数组
        """
        try:
            # 复制图像以避免修改原图
            annotated_image = image_array.copy()

            for detection in detections:
                bbox = detection["bbox"]
                class_name = detection["class_name"]
                confidence = detection["confidence"]

                # 绘制边界框
                x1, y1, x2, y2 = int(bbox["x1"]), int(bbox["y1"]), int(bbox["x2"]), int(bbox["y2"])
                cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制标签
                label = f"{class_name}: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(annotated_image, (x1, y1 - label_size[1] - 10),
                             (x1 + label_size[0], y1), (0, 255, 0), -1)
                cv2.putText(annotated_image, label, (x1, y1 - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

            return annotated_image

        except Exception as e:
            self.logger.error(f"图像标注失败: {str(e)}")
            return image_array

    def create_annotated_image_base64(self, image_array: np.ndarray, detections: List[Dict[str, Any]]) -> str:
        """
        创建标注图像并转换为base64编码

        Args:
            image_array: 原始图像数组
            detections: 检测结果列表

        Returns:
            str: base64编码的图像数据
        """
        try:
            annotated_image = self.create_annotated_image(image_array, detections)

            # 转换为base64编码
            _, buffer = cv2.imencode('.jpg', annotated_image)
            image_base64 = base64.b64encode(buffer).decode('utf-8')

            return f"data:image/jpeg;base64,{image_base64}"

        except Exception as e:
            self.logger.error(f"创建base64标注图像失败: {str(e)}")
            return ""

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            "model_loaded": self.model is not None,
            "model_path": self.model_path,
            "is_warmed_up": self.is_warmed_up,
            "supported_formats": list(self.allowed_extensions),
            "default_confidence_threshold": self.config.model.confidence_threshold
        }
