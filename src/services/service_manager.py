#!/usr/bin/env python3
"""
服务管理模块
提供服务启动、停止和管理的统一入口
"""

import os
import sys
import signal
import logging
import argparse
from pathlib import Path
from typing import Optional

from ..api.app import create_application
from ..core.config import get_config_manager
from ..utils.logging_config import get_logger


class ServiceManager:
    """服务管理器类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化服务管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config_manager = get_config_manager(config_file)
        self.config = self.config_manager.get_config()
        self.logger = get_logger(__name__)
        self.application = None
        self.pid_file = Path("yolo_api_server.pid")
    
    def start_service(self, daemon: bool = False) -> bool:
        """
        启动服务
        
        Args:
            daemon: 是否以守护进程模式运行
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 检查是否已经在运行
            if self.is_service_running():
                self.logger.warning("服务已经在运行中")
                return False
            
            self.logger.info("正在启动服务...")
            
            # 创建应用程序
            self.application = create_application(self.config_file)
            
            # 写入PID文件
            self._write_pid_file()
            
            # 注册信号处理器
            self._register_signal_handlers()
            
            if daemon:
                self._run_as_daemon()
            else:
                self._run_foreground()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            return False
    
    def stop_service(self) -> bool:
        """
        停止服务
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_service_running():
                self.logger.info("服务未在运行")
                return True
            
            # 读取PID
            pid = self._read_pid_file()
            if pid:
                self.logger.info(f"正在停止服务 (PID: {pid})...")
                os.kill(pid, signal.SIGTERM)
                
                # 等待进程结束
                import time
                for _ in range(10):  # 等待最多10秒
                    if not self.is_service_running():
                        break
                    time.sleep(1)
                
                # 如果还在运行，强制终止
                if self.is_service_running():
                    self.logger.warning("正常停止失败，强制终止服务")
                    os.kill(pid, signal.SIGKILL)
            
            # 清理PID文件
            self._cleanup_pid_file()
            self.logger.info("服务已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止服务失败: {e}")
            return False
    
    def restart_service(self, daemon: bool = False) -> bool:
        """
        重启服务
        
        Args:
            daemon: 是否以守护进程模式运行
            
        Returns:
            bool: 重启是否成功
        """
        self.logger.info("正在重启服务...")
        
        # 停止服务
        if not self.stop_service():
            return False
        
        # 等待一下
        import time
        time.sleep(2)
        
        # 启动服务
        return self.start_service(daemon)
    
    def get_service_status(self) -> dict:
        """
        获取服务状态
        
        Returns:
            dict: 服务状态信息
        """
        is_running = self.is_service_running()
        pid = self._read_pid_file() if is_running else None
        
        status = {
            "running": is_running,
            "pid": pid,
            "config_file": self.config_file,
            "api_host": self.config.api.host,
            "api_port": self.config.api.port,
            "pid_file": str(self.pid_file)
        }
        
        if self.application:
            services = self.application.get_services()
            status.update({
                "model_loaded": services['inference_service'].get_model_info()['model_loaded'] if services['inference_service'] else False,
                "database_connected": services['db_manager'] is not None,
                "storage_initialized": services['image_storage'] is not None
            })
        
        return status
    
    def is_service_running(self) -> bool:
        """
        检查服务是否在运行
        
        Returns:
            bool: 服务是否在运行
        """
        pid = self._read_pid_file()
        if not pid:
            return False
        
        try:
            # 发送信号0检查进程是否存在
            os.kill(pid, 0)
            return True
        except (OSError, ProcessLookupError):
            # 进程不存在，清理PID文件
            self._cleanup_pid_file()
            return False
    
    def _run_foreground(self):
        """前台运行服务"""
        self.logger.info("服务启动成功，前台运行模式")
        self.application.run()
    
    def _run_as_daemon(self):
        """以守护进程模式运行服务"""
        # 简化的守护进程实现
        try:
            pid = os.fork()
            if pid > 0:
                # 父进程退出
                sys.exit(0)
        except OSError as e:
            self.logger.error(f"创建守护进程失败: {e}")
            sys.exit(1)
        
        # 子进程继续
        os.chdir("/")
        os.setsid()
        os.umask(0)
        
        # 重定向标准输入输出
        with open('/dev/null', 'r') as f:
            os.dup2(f.fileno(), sys.stdin.fileno())
        with open('/dev/null', 'w') as f:
            os.dup2(f.fileno(), sys.stdout.fileno())
            os.dup2(f.fileno(), sys.stderr.fileno())
        
        self.logger.info("服务启动成功，守护进程模式")
        self.application.run()
    
    def _write_pid_file(self):
        """写入PID文件"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            self.logger.error(f"写入PID文件失败: {e}")
    
    def _read_pid_file(self) -> Optional[int]:
        """
        读取PID文件
        
        Returns:
            Optional[int]: PID，如果文件不存在或无效则返回None
        """
        try:
            if self.pid_file.exists():
                with open(self.pid_file, 'r') as f:
                    return int(f.read().strip())
        except (ValueError, IOError):
            pass
        return None
    
    def _cleanup_pid_file(self):
        """清理PID文件"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
        except Exception as e:
            self.logger.error(f"清理PID文件失败: {e}")
    
    def _register_signal_handlers(self):
        """注册信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"收到信号 {signum}，正在关闭服务...")
            self._cleanup_pid_file()
            sys.exit(0)
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)


def main():
    """主函数 - 命令行入口"""
    parser = argparse.ArgumentParser(description="YOLO推理服务管理器")
    parser.add_argument("action", choices=["start", "stop", "restart", "status"],
                       help="要执行的操作")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--daemon", "-d", action="store_true",
                       help="以守护进程模式运行")
    
    args = parser.parse_args()
    
    # 创建服务管理器
    service_manager = ServiceManager(args.config)
    
    # 执行操作
    if args.action == "start":
        success = service_manager.start_service(args.daemon)
        sys.exit(0 if success else 1)
    
    elif args.action == "stop":
        success = service_manager.stop_service()
        sys.exit(0 if success else 1)
    
    elif args.action == "restart":
        success = service_manager.restart_service(args.daemon)
        sys.exit(0 if success else 1)
    
    elif args.action == "status":
        status = service_manager.get_service_status()
        print("服务状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        sys.exit(0)


if __name__ == "__main__":
    main()
