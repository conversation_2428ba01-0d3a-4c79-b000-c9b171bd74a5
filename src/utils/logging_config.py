#!/usr/bin/env python3
"""
日志配置模块
提供统一的日志配置功能，支持配置文件和环境变量
"""

import logging
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Optional

from ..core.config import get_config


def setup_logging(logger_name: Optional[str] = None) -> logging.Logger:
    """
    设置日志配置，支持文件滚动

    Args:
        logger_name: 日志器名称，如果为None则使用调用模块的名称

    Returns:
        配置好的日志器
    """
    # 获取配置
    config = get_config()
    log_config = config.logging

    # 创建日志文件夹
    log_dir = Path(log_config.log_dir)
    log_dir.mkdir(exist_ok=True)

    # 创建logger
    if logger_name is None:
        logger_name = __name__
    logger = logging.getLogger(logger_name)

    # 设置日志级别
    log_level = getattr(logging, log_config.level.upper(), logging.INFO)
    logger.setLevel(log_level)

    # 清除已有的处理器
    logger.handlers.clear()

    # 创建文件处理器 - 滚动日志文件
    log_file = log_dir / "yolo_api.log"
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=log_config.max_file_size_mb * 1024 * 1024,
        backupCount=log_config.backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)

    # 创建格式器
    formatter = logging.Formatter(
        log_config.format,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器

    Args:
        name: 日志器名称

    Returns:
        日志器实例
    """
    return setup_logging(name)