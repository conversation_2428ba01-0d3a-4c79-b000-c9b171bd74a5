#!/usr/bin/env python3
"""
YOLO API服务测试脚本
用于测试yolo_api_server.py提供的API服务

使用方法:
1. 启动API服务: python yolo_api_server.py
2. 运行测试: python test_api.py [image_path]
"""

import os
import sys
import json
import requests
import argparse
from pathlib import Path

from ultralytics import YOL<PERSON>

def test_health_check(base_url):
    """测试健康检查端点"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_prediction(base_url, image_path, confidence=0.1, return_image=False):
    """测试图像推理端点"""
    print(f"🔮 测试图像推理: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    try:
        with open(image_path, 'rb') as f:
            files = {'image': f}
            data = {
                'confidence': confidence,
                'return_image': str(return_image).lower()
            }
            
            response = requests.post(f"{base_url}/predict", files=files, data=data)
            
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 推理成功!")
            print(f"detection result: {result}")
            print(f"检测到 {result['detections_count']} 个对象:")
            
            for i, detection in enumerate(result['detections'], 1):
                print(f"  {i}. {detection['class_name']} (置信度: {detection['confidence']:.3f})")
                bbox = detection['bbox']
                print(f"     位置: ({bbox['x1']:.1f}, {bbox['y1']:.1f}) - ({bbox['x2']:.1f}, {bbox['y2']:.1f})")
            
            # 如果返回了标注图像，保存到文件
            if 'annotated_image' in result:
                import base64
                image_data = result['annotated_image'].split(',')[1]
                image_bytes = base64.b64decode(image_data)
                
                output_path = f"annotated_{Path(image_path).name}"
                with open(output_path, 'wb') as f:
                    f.write(image_bytes)
                print(f"📸 标注图像已保存: {output_path}")
            
            return True
        else:
            print(f"❌ 推理失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def find_test_images():
    """查找可用的测试图像"""
    test_dirs = [
        "YOLO_outputs/20250630pm_reannotated_yolo11n_100",
        "YOLO_outputs/v11n_b16_best",
        "YOLO_outputs/yolov11n_200_best",
        "."
    ]
    
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    return os.path.join(test_dir, file)
    
    return None

def main():
    parser = argparse.ArgumentParser(description='测试YOLO API服务')
    parser.add_argument('image',default='/Users/<USER>/Downloads/Data_2025710170523/Image_20250710165958841.jpg', nargs='?', help='测试图像路径')
    parser.add_argument('--url', default='http://***************:5000', help='API服务地址')
    parser.add_argument('--confidence', type=float, default=0.1, help='置信度阈值')
    parser.add_argument('--return-image', action='store_true',default=True, help='返回标注后的图像')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("YOLO API服务测试")
    print("=" * 60)
    print(f"API地址: {args.url}")
    
    # 测试健康检查
    if not test_health_check(args.url):
        print("❌ 服务不可用，请确保API服务正在运行")
        return
    
    print()
    
    # 确定测试图像
    image_path = args.image
    if not image_path:
        image_path = find_test_images()
        if not image_path:
            print("❌ 未找到测试图像，请指定图像路径")
            print("使用方法: python test_api.py path/to/image.jpg")
            return
        print(f"🖼️  使用自动找到的测试图像: {image_path}")
    
    # 测试推理
    success = test_prediction(
        args.url, 
        image_path, 
        args.confidence, 
        args.return_image
    )
    
    print()
    if success:
        print("✅ 所有测试通过!")
    else:
        print("❌ 测试失败!")

if __name__ == '__main__':
    # model = YOLO("/Users/<USER>/Downloads/PACK_AOI_best.pt")
    # print(model.names)
    # results = model.predict(source="/Users/<USER>/Documents/Workspace/projects/aec_pack_dispensing_inspection/Image_20250711152630823.jpg")
    # print(results)
    main()
