#!/usr/bin/env python3
"""
API集成测试
"""

import unittest
import tempfile
import os
import sys
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.api.app import create_app


class TestAPIIntegration(unittest.TestCase):
    """API集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试配置文件
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
        test_config = {
            "database": {
                "path": os.path.join(self.temp_dir, "test.db"),
                "schema_file": "database_schema.sql"
            },
            "storage": {
                "base_path": os.path.join(self.temp_dir, "test_storage")
            },
            "api": {
                "debug": True,
                "port": 5001
            },
            "model": {
                "model_path": "test_model.pt",  # 不存在的模型文件用于测试
                "warmup_enabled": False
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
        
        # 创建Flask测试客户端
        try:
            self.app = create_app(self.config_file)
            self.client = self.app.test_client()
            self.app_context = self.app.app_context()
            self.app_context.push()

            # 设置测试请求头
            self.test_headers = {
                'X-Test-Request': 'true',
                'User-Agent': 'AEC-Integration-Test/1.0'
            }
        except Exception as e:
            # 如果创建应用失败（比如缺少模型文件），跳过测试
            self.skipTest(f"无法创建测试应用: {e}")
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'app_context'):
            self.app_context.pop()
        
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_health_check(self):
        """测试健康检查端点"""
        response = self.client.get('/health', headers=self.test_headers)
        
        self.assertIn(response.status_code, [200, 503])  # 可能因为模型未加载返回503
        
        data = json.loads(response.data)
        self.assertIn('status', data)
        self.assertIn('timestamp', data)
        self.assertIn('model_loaded', data)
    
    def test_index_page(self):
        """测试主页"""
        response = self.client.get('/')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('text/html', response.content_type)
    
    def test_statistics_api(self):
        """测试统计API"""
        response = self.client.get('/api/statistics')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('success', data)
        self.assertIn('api_statistics', data)
        self.assertIn('timing_statistics', data)
    
    def test_false_positive_reports_api(self):
        """测试误报记录API"""
        response = self.client.get('/api/false-positive/reports')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('success', data)
        self.assertIn('reports', data)
        self.assertIn('total_count', data)
    
    def test_predict_without_file(self):
        """测试没有文件的推理请求"""
        response = self.client.post('/predict')

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertEqual(data['error'], '缺少图像文件')

    def test_false_positive_report_api(self):
        """测试误报上报API"""
        # 测试无数据请求
        response = self.client.post('/api/false-positive/report',
                                  headers=self.test_headers)
        self.assertEqual(response.status_code, 400)

        data = json.loads(response.data)
        self.assertIn('error', data)

        # 测试缺少必需字段
        response = self.client.post('/api/false-positive/report',
                                  json={'session_id': 'test'},
                                  headers=self.test_headers)
        self.assertEqual(response.status_code, 400)

        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertIn('缺少必需字段', data['error'])

        # 测试无效的误报类型
        response = self.client.post('/api/false-positive/report',
                                  json={
                                      'session_id': 'test',
                                      'false_positive_type': 'invalid_type'
                                  },
                                  headers=self.test_headers)
        self.assertEqual(response.status_code, 400)

        data = json.loads(response.data)
        self.assertIn('error', data)
        self.assertIn('无效的误报类型', data['error'])


if __name__ == '__main__':
    unittest.main()
