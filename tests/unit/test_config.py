#!/usr/bin/env python3
"""
配置管理模块单元测试
"""

import unittest
import tempfile
import json
import os
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.core.config import ConfigManager, AppConfig


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_config(self):
        """测试默认配置"""
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        self.assertIsInstance(config, AppConfig)
        self.assertEqual(config.api.port, 5000)
        self.assertEqual(config.database.path, "yolo_inference.db")
    
    def test_config_from_file(self):
        """测试从文件加载配置"""
        # 创建测试配置文件
        test_config = {
            "api": {
                "port": 8080,
                "host": "127.0.0.1"
            },
            "database": {
                "path": "test.db"
            }
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
        
        # 加载配置
        config_manager = ConfigManager(self.config_file)
        config = config_manager.get_config()
        
        self.assertEqual(config.api.port, 8080)
        self.assertEqual(config.api.host, "127.0.0.1")
        self.assertEqual(config.database.path, "test.db")
    
    def test_config_from_env(self):
        """测试从环境变量加载配置"""
        # 设置环境变量
        os.environ['AEC_API_PORT'] = '9000'
        os.environ['AEC_DB_PATH'] = 'env_test.db'
        
        try:
            config_manager = ConfigManager()
            config = config_manager.get_config()
            
            self.assertEqual(config.api.port, 9000)
            self.assertEqual(config.database.path, 'env_test.db')
        finally:
            # 清理环境变量
            os.environ.pop('AEC_API_PORT', None)
            os.environ.pop('AEC_DB_PATH', None)
    
    def test_save_config(self):
        """测试保存配置"""
        config_manager = ConfigManager()
        output_file = os.path.join(self.temp_dir, "output_config.json")
        
        config_manager.save_config(output_file)
        
        self.assertTrue(os.path.exists(output_file))
        
        # 验证保存的配置
        with open(output_file, 'r') as f:
            saved_config = json.load(f)
        
        self.assertIn('api', saved_config)
        self.assertIn('database', saved_config)


if __name__ == '__main__':
    unittest.main()
