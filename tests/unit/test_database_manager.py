#!/usr/bin/env python3
"""
数据库管理模块单元测试
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.core.database_manager import DatabaseManager


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        
        # 创建简单的测试架构文件
        self.schema_file = os.path.join(self.temp_dir, "test_schema.sql")
        with open(self.schema_file, 'w') as f:
            f.write("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        db_manager = DatabaseManager(self.db_path, self.schema_file)
        
        # 检查数据库文件是否创建
        self.assertTrue(os.path.exists(self.db_path))
        
        # 检查表是否创建
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_table'")
            result = cursor.fetchone()
            self.assertIsNotNone(result)
    
    def test_connection_context_manager(self):
        """测试连接上下文管理器"""
        db_manager = DatabaseManager(self.db_path, self.schema_file)
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test_name",))
            conn.commit()
        
        # 验证数据是否插入
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM test_table WHERE name = ?", ("test_name",))
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result['name'], "test_name")
    
    def test_session_id_generation(self):
        """测试会话ID生成"""
        db_manager = DatabaseManager(self.db_path, self.schema_file)
        
        session_id1 = db_manager.generate_session_id()
        session_id2 = db_manager.generate_session_id()
        
        self.assertIsInstance(session_id1, str)
        self.assertIsInstance(session_id2, str)
        self.assertNotEqual(session_id1, session_id2)
    
    def test_file_hash_calculation(self):
        """测试文件哈希计算"""
        db_manager = DatabaseManager(self.db_path, self.schema_file)
        
        test_data = b"test data for hashing"
        
        # 测试MD5
        md5_hash = db_manager.calculate_file_hash(test_data, 'md5')
        self.assertIsInstance(md5_hash, str)
        self.assertEqual(len(md5_hash), 32)  # MD5哈希长度
        
        # 测试SHA256
        sha256_hash = db_manager.calculate_file_hash(test_data, 'sha256')
        self.assertIsInstance(sha256_hash, str)
        self.assertEqual(len(sha256_hash), 64)  # SHA256哈希长度
        
        # 测试不支持的算法
        with self.assertRaises(ValueError):
            db_manager.calculate_file_hash(test_data, 'unsupported')


if __name__ == '__main__':
    unittest.main()
